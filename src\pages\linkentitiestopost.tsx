import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import Checkbox from "../components/Checkbox";
import apiService from "../services/apiService";
import Loader from "./loader";

interface Association {
  AttributeId: string;
  Key: string;
  Value: string;
}

interface ApiResponse {
  associations: Association[];
}

function LinkEntitiesToPost() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [attributes, setAttributeData] = useState<any[]>([]);
  const [selectedAttributes, setSelectedAttributes] = useState<string[]>([]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await apiService.get("attributes");
      setAttributeData(response as any[]);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllData = async () => {
    if (!id) return;
    setLoading(true);
    try {
      const response = await apiService.get<ApiResponse>(
        `/attribute-associations/publication-attribute-association?publicationId=${id}`
      );

      if (response?.associations) {
        const linkedIds = response.associations.map(
          (assoc) => assoc.AttributeId
        );
        setSelectedAttributes(linkedIds);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    fetchAllData();
  }, [id]);

  const handleCheckboxChange = (attributeId: string) => {
    setSelectedAttributes((prev) =>
      prev.includes(attributeId)
        ? prev.filter((id) => id !== attributeId)
        : [...prev, attributeId]
    );
  };

  const handleLinkEntities = async () => {
    if (!id) return;
    setLoading(true);
    try {
      await apiService.post("attribute-associations/update", {
        publicationId: id,
        attributeIds: selectedAttributes,
      });
      navigate("/publications");
    } catch (error) {
      console.error("Error linking entities:", error);
    } finally {
      setLoading(false);
    }
  };

  const groupedAttributes = attributes.reduce(
    (acc: Record<string, any[]>, attr) => {
      if (!acc[attr.Key]) acc[attr.Key] = [];
      acc[attr.Key].push(attr);
      return acc;
    },
    {}
  );
  return (
    <div className="mt-4">
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="flex justify-between items-center my-4">
            <h1 className="font-bold text-xl xs:max-sm:text-lg xs:max-sm:max-w-[120px]">
              {t("linkEntitie.linkEntitiesToPostTitle")}
            </h1>
            <div className="flex xs:max-sm:flex-col items-center gap-3 xs:max-sm:gap-1 justify-start">
              <button
                className="bg-primary font-semibold xs:max-sm:font-medium xs:max-sm:rounded-lg xs:max-sm:text-xs xs:max-sm:w-[100px] flex items-center justify-center gap-2 btn text-[#ffffff] py-3 px-4 rounded-xl"
                onClick={handleLinkEntities}
                disabled={loading}
              >
                {t("linkEntitie.linkEntitiesButton")}
              </button>
              <button
                className="bg-[#4B5563] flex items-center justify-center xs:max-sm:w-[100px] gap-2 btn text-[#ffffff] font-semibold xs:max-sm:font-medium xs:max-sm:rounded-lg xs:max-sm:text-xs py-3 px-4 text-md rounded-xl"
                onClick={() => navigate("/publications")}
                disabled={loading}
              >
                {t("linkEntitie.cancelButton")}
              </button>
            </div>
          </div>

          <div className="shadow bg-white rounded-2xl overflow-hidden">
            <table className="w-full table-auto">
              <thead className="bg-[#F3FAFD]">
                <tr>
                  <th className="text-left px-8 py-3 text-lg font-medium text-gray-600">
                    Entities
                  </th>
                  <th className="text-left px-8 py-3 text-lg font-medium text-gray-600">
                    Link
                  </th>
                </tr>
              </thead>
              <tbody>
                {Object.entries(groupedAttributes).map(
                  ([key, values], index) => (
                    <tr
                      key={key}
                      className={`${
                        index % 2 == 0 ? "" : "bg-gray-50"
                      } xs:max-sm:flex-col xs:max-sm:flex xs:max-sm:w-[100vw]`}
                    >
                      <td className="px-6 py-4 text-md font-bold tracking-tight text-gray-900 whitespace-nowrap">
                        {key}
                      </td>
                      <td className="px-6 py-4 xs:max-sm:py-0 xs:max-sm:px-0">
                        <div className="flex flex-wrap gap-4">
                          {values.map((item) => (
                            <label
                              key={item.AttributeId}
                              className="flex items-center gap-2 xs:max-sm:text-sm px-3 py-2 rounded-md"
                            >
                              <Checkbox
                                isChecked={selectedAttributes.includes(
                                  item.AttributeId
                                )}
                                onChange={() =>
                                  handleCheckboxChange(item.AttributeId)
                                }
                                className="xs:max-sm:h-4 xs:max-sm:w-4 xs:max-sm:rounded"
                                disabled={loading}
                              />
                              <span className="text-gray-800">
                                {item.Value}
                              </span>
                            </label>
                          ))}
                        </div>
                      </td>
                    </tr>
                  )
                )}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
}

export default LinkEntitiesToPost;
