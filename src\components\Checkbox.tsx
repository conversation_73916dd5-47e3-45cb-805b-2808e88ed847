import { IoCheckmarkSharp } from "react-icons/io5";
interface CheckboxProps {
  onChange: () => void;
  isChecked?: boolean;
  disabled?: boolean;
  className?: string;
}
const Checkbox: React.FC<CheckboxProps> = ({
  onChange,
  isChecked = true,
  disabled = false,
  className = "",
}) => {
  const handleClick = () => {
    if (!disabled) {
      onChange();
    }
  };
  return (
    <div
      role="checkbox"
      aria-checked={isChecked}
      aria-disabled={disabled}
      onClick={handleClick}
      className={`flex h-5 w-5 items-center justify-center rounded-md 
        ${
          disabled
            ? "cursor-not-allowed bg-gray-200"
            : "cursor-pointer bg-lightblue"
        } 
        ${
          !isChecked ? "border border-gray-300 bg-transparent" : ""
        } ${className}`}
    >
      {isChecked && <IoCheckmarkSharp className="text-primary text-sm" />}
    </div>
  );
};

export default Checkbox;