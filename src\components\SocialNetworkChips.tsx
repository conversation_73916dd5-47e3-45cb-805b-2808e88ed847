import React from "react";
import { Fa<PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>ram, FaXTwitter } from "react-icons/fa6";
import { RiYoutubeFill } from "react-icons/ri";

interface Option {
  name: string;
  icon: React.ReactElement;
  color: string;
}

interface SocialNetworkChipsProps {
  socialNetwork: string[];
  setSocialNetwork: React.Dispatch<React.SetStateAction<string[]>>;
  t: (key: string) => string;
}

const options: Option[] = [
  {
    name: "Facebook",
    icon: (
      <>
        <span className="h-6 w-6 bg-red-200">
          <FaFacebookF
            className="text-3xl bg-[#1877F2] p-1.5 text-[#fff] rounded-lg"
            style={{
              boxShadow: "0px 3px 10px 0px #1976D252",
            }}
          />
        </span>
      </>
    ),
    color: "#1877F2",
  },
  {
    name: "Youtube",
    icon: (
      <span className="h-6 w-6 bg-red-200">
        <RiYoutubeFill
          className="text-3xl bg-[#E21A20] p-1.5 text-[#fff] rounded-lg"
          style={{
            boxShadow: "0px 4px 4px 0px #E21A204F",
          }}
        />
      </span>
    ),
    color: "#FF0000",
  },
  {
    name: "Twitter",
    icon: (
      <span className="h-6 w-6 bg-red-200">
        <FaXTwitter
          className="text-3xl bg-[#000] p-1.5 text-[#fff] rounded-lg"
          style={{
            boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.2)",
          }}
        />
      </span>
    ),
    color: "#333333",
  },

  {
    name: "Instagram",
    icon: (
      <span className="h-6 w-6 bg-red-200">
        <FaInstagram
          className="text-3xl p-1.5 text-white rounded-lg"
          style={{
            background:
              "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
            boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
          }}
        />
      </span>
    ),
    color: "#E1306C",
  },
];

const SocialNetworkChips: React.FC<SocialNetworkChipsProps> = ({
  socialNetwork,
  setSocialNetwork,
  t,
}) => {
  const handleToggle = (name: string) => {
    if (socialNetwork.includes(name)) {
      setSocialNetwork(socialNetwork.filter((item) => item !== name));
    } else {
      setSocialNetwork([...socialNetwork, name]);
    }
  };

  return (
    <div className="min-w-[200px] xs:max-sm:w-xs">
      <label htmlFor="social-network" className="text-[#5A5A5A] mb-2 block">
        {t("groupPosts.channel")}
      </label>
      <div className="flex gap-3">
        {options.map((option) => {
          const isSelected = socialNetwork.includes(option.name);
          return (
            <div
              key={option.name}
              className="relative cursor-pointer"
              onClick={() => handleToggle(option.name)}
            >
              {option.icon}
              <div
                className="absolute h-3 w-3 right-[-5px] top-5 rounded-full"
                style={{
                  backgroundColor: isSelected ? option.color : "transparent",
                  border: isSelected ? "2.5px solid #fff" : "none",
                }}
              ></div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SocialNetworkChips;