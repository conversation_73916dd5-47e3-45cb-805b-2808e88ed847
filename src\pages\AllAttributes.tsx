
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import apiService from "../services/apiService";
import Loader from "./loader";
import { Switch } from "@headlessui/react";
import Pagination from "../components/Pagination";

interface Attribute {
  AttributeId: string;
  Key: string;
  Value: string;
  thumbnail?: string;
  IsDeleted: boolean;
}

function AllAttributes() {
  const { t } = useTranslation();
  const [data, setData] = useState<Attribute[]>([]);
  const [filteredData, setFilteredData] = useState<Attribute[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Pagination calculations
  const totalItems = filteredData.length;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  const paginatedData = filteredData.slice(startIndex, endIndex);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= Math.ceil(totalItems / itemsPerPage)) {
      setCurrentPage(newPage);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await apiService.get<Attribute[]>("/attributes/all");
      setData(response);
      setFilteredData(response); // Initialize filteredData with fetched data
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = async (attributeId: string, currentStatus: boolean) => {
    try {
      // Prepare bulk update payload
      const payload = {
        updates: [
          {
            id: attributeId,
            isDeleted: !currentStatus,
          },
        ],
      };
      await apiService.post(`/attributes/bulk-update`, payload);
      const updatedData = data.map((item) =>
        item.AttributeId === attributeId
          ? { ...item, IsDeleted: !currentStatus }
          : item
      );
      setData(updatedData);
      setFilteredData(updatedData); // Update filteredData to reflect toggle changes
    } catch (error) {
      console.error("Error updating toggle:", error);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="mt-4 xs:max-sm:px-2">
      <div className="mt-6 bg-gray-100">
        <div className="bg-white rounded-2xl overflow-x-auto">
          <div className="w-full overflow-x-auto">
            <table className="min-w-full border-collapse text-left">
              <thead>
                <tr className="border-b border-gray-100 font-bold tracking-tight bg-[#F3FAFD] text-sm">
                  <th className="py-4 pl-6 pr-4 xs:max-sm:px-0 xs:max-sm:pl-2 xs:max-sm:text-[12px]">
                    {t("SettingsAttributes.entityKey")}
                  </th>
                  <th className="p-4 xs:max-sm:px-0 xs:max-sm:text-[12px]">
                    {t("SettingsAttributes.entityName")}
                  </th>
                  <th className="p-4 xs:max-sm:px-0 xs:max-sm:text-[12px]">
                    {t("SettingsAttributes.image")}
                  </th>
                  <th className="py-4 pl-4 pr-6 xs:max-sm:px-0 xs:max-sm:text-[12px]">
                    {t("SettingsAttributes.actions")}
                  </th>
                </tr>
              </thead>
              <tbody>
                {loading && !paginatedData.length ? (
                  <tr>
                    <td colSpan={4} className="p-4 text-center">
                      <Loader />
                    </td>
                  </tr>
                ) : paginatedData.length > 0 ? (
                  paginatedData.map((item) => (
                    <tr
                      key={item.AttributeId}
                      className="border-b border-gray-100 hover:bg-gray-50"
                    >
                      <td className="py-4 pl-6 pr-4 xs:max-sm:px-2">{item.Key}</td>
                      <td className="p-4">{item.Value}</td>
                      <td className="p-4">
                        <div className="relative w-16 h-16 xs:max-sm:w-10 xs:max-sm:h-10 group">
                          <img
                            src={`${item.thumbnail}?t=${Date.now()}`}
                            alt={item.AttributeId || "Thumbnail"}
                            className="w-12 h-12 object-cover rounded-md"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.onerror = null;
                              target.src =
                              "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANkAAACUCAMAAAAppnz2AAAAQlBMVEX///+8vb/v8PC1trnKy8zGyMf6+/rj5OS5u77///319fbHx8nHx8u6vsG5u7y8vcHU1dbe3t7q6uu1tbTa2t2+wb80IpWwAAAE80lEQVR4nO2d2ZajIBBAg9qGYBqXMf//q6O4BBewQCBlmvsyL4nhHqiiWOy53SKRSCQSiUQwkF+TY7Hk9XM9nsUrOTZLyRX5F80uh97scWGzFGxGeQ9FT9dIM7Oivl+F+mliRp+A+QEJrKAmZgUL0ywHfKnZ4/H4UrOuzYZmz6uYGY/GaIaAaBbN8BDNohkeolk0w0M0i2Z4iGYfNMtZh/nXsJsl9yrLiiKr6sbwm7jNWPaadjc5+bmbfRexGatSQt/bvTTNTH4dsVmSrTfcOTEYknjNupaV6116SuAjEq9ZQddeguNToxG0ZtX+2Q98xxOrWbPr1Te1Aj4BqVme7Y/Fvg3AQxOkZsou6xJkDXsEUrOaq9VK2CNwmuWKxDgAS49IzTRehMPmNJxmTHfcDww0nGbaiwyXNvvePvtes5xosj6HFfxIzTKN2QtWhOA0083UNIM9AqlZ8lKapcDVJ1KzW6XqNEqAT8BqlpzMH3jNbvf9ypFDl2d4zfYX1TwDX2nDa5bvhBo3uB2F1+x2aym17jG8Zo/+R5NM2kmlvDDaJA5s9hAtBn1Q/HOvCiLu93KS1Wa3RoOasQSuNpInTV1Xbd0kprdhQ5rlP6/kZmhmT0AzVnBKwVu8pwln1on16S2YWjAzIfb7S0vTEz77HwxjlhfjvEvLQL0WyExaJFMaptfCmDF5ZzRQGglixoplCRgk1kKYiRj7lcyCxFoAMxFjv7JZkFjzbzYMxYVYkFjzbsae+4vjLtb81lm+zfJCsVXjPdY8m2k2eyl4r8YOv2ZsnRVluN8a0qsZyzZZcWH26nvN17LGp9mi8tgdkKQxXooa/roXM+2R7KhGmwv22bqkUgxJb7HmzYzpDoqWA9IPvsy0WVGm9FUeezLLX7qsGKTX/JjBYmxSg5fHjWgKLOd4MTMSM+i1Nq0fU3sAjXBvdjiPWaq1lNAaPEd4MBOXpkAhNjNUIwe03UAoP3l+xvpTBljykNSOy+O2a0DZ39wE9ppzM/azs9AEmA2FlrrV7TTC+TggjwRdmxkmjzf9FitEjIyxdpggHZtZixH9jtbi/JOCYs2tGdPdKjqEKxc17fLMGnRL2qlZUhDzEJP6op+yd8zydj2LpO1xGnFpJp5lmhUlymG9tnluL7Z63YIe35dzaHYmxqYGk2Z74tvuPXZU0+QRd2bnYmxq8HYpqnjZIq3yQGYOemxSk8l3e6znqBpxZeZIjIzJf+6LTfKQ1bSH8o7MmHmtqGKMtbfY5l2t+ZPaNOLGjFHwQhOgxudYG2tFFamu15yYJc6G4qA2xpo6xt5q6qWoCzMxQbtFxFp7/Dnezg30YKZ/icUKEWua5CF9slW1+rxZnjodimODeaOYx9YoY+20GXj7zVCtn/c1yUNW25+tz5olJ2tFB0xT9krwpJmHGDOHVn1yXJeb58zYp6UGhmrEZZ85KYJdsFdDnjFLMAzFAb5dip4w85QV7djujdibsdJm+80bm3nN2gxNjE2M2z5zhrQ1c7cec8YQa3O8WZoZH0qEYBlrdmZiHkMTYjOLVbaVGesLus+WVPvIacTm7xE7Xmi6RKohLcwQTdBbaDUdZ5iboUv3S+ZCy9gMudg71kzNGkwl1T7jUtTMjBBx2oLbjNKq7mjFvhPc7BKI/+1hSHNfZiYRza7H3zYrsitShHtJMRKJRCKRSORP8h+ptHV/XdGNDgAAAABJRU5ErkJggg==";
                            }}
                          />
                        </div>
                      </td>
                      <td className="py-4 pl-4 pr-6 xs:max-sm:px-2">
                        <div
                          title={item.IsDeleted ? t("SettingsAttributes.enableTooltip") : t("SettingsAttributes.disableTooltip")}
                          className="inline-block"
                        >
                          <Switch
                            checked={item.IsDeleted}
                            onChange={() => handleToggle(item.AttributeId, item.IsDeleted)}
                            className={`${item.IsDeleted ? "bg-primary" : "bg-gray-300"
                              } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none`}
                          >
                            <span
                              className={`${item.IsDeleted ? "translate-x-6" : "translate-x-1"
                                } inline-block h-4 w-4 transform bg-white rounded-full transition-transform`}
                            />
                          </Switch>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="text-center py-4">
                      {t("common.noDataAvailable")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            <div className="mt-4 px-8">
              <Pagination
                currentPage={currentPage}
                totalItems={filteredData.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AllAttributes;