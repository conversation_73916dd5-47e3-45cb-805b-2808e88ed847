// import React, { createContext, useContext, useState, useEffect } from "react";
// import { useNavigate } from "react-router-dom";

// interface AuthContextType {
//   isAuthenticated: boolean;
//   login: (token: string) => void;
//   logout: () => void;
// }

// const AuthContext = createContext<AuthContextType | null>(null);

// export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
//   const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
//   const navigate = useNavigate();

//   useEffect(() => {
//     const token = localStorage.getItem("authToken");
//     setIsAuthenticated(!!token);
//   }, []);

//   const login = (token: string) => {
//     localStorage.setItem("authToken", token);
//     setIsAuthenticated(true);
//     navigate("/group-posts");
//   };

//   const logout = () => {
//     localStorage.removeItem("authToken");
//     localStorage.remove("UserData");
//     localStorage.removeItem("powerbi_token");
//     setIsAuthenticated(false);

//     setTimeout(() => {
//       window.location.href = "/login";
//     }, 100);
//   };

//   if (isAuthenticated === null) {
//     return <div>Loading...</div>;
//   }

//   return (
//     <AuthContext.Provider value={{ isAuthenticated, login, logout }}>
//       {children}
//     </AuthContext.Provider>
//   );
// };

// export const useAuth = () => {
//   const context = useContext(AuthContext);
//   if (!context) {
//     throw new Error("useAuth must be used within an AuthProvider");
//   }
//   return context;
// };

import React, { createContext, useContext, useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

interface UserData {
  role?: string;
  // Add other user data fields as needed
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: UserData | null;
  login: (token: string, userData?: UserData) => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [user, setUser] = useState<UserData | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem("authToken");
    const storedUserData = localStorage.getItem("UserData");
    if (token) {
      setIsAuthenticated(true);
      if (storedUserData) {
        setUser(JSON.parse(storedUserData));
      }
    } else {
      setIsAuthenticated(false);
      setUser(null);
    }
  }, []);

  const login = (token: string, userData?: UserData) => {
    localStorage.setItem("authToken", token);
    if (userData) {
      localStorage.setItem("UserData", JSON.stringify(userData));
      setUser(userData);
    }
    setIsAuthenticated(true);
    navigate("/group-posts");
  };

  const logout = () => {
    localStorage.removeItem("authToken");
    localStorage.removeItem("UserData");
    localStorage.removeItem("powerbi_token");
    setIsAuthenticated(false);
    setUser(null);

    setTimeout(() => {
      window.location.href = "/login";
    }, 100);
  };

  if (isAuthenticated === null) {
    return <div>Loading...</div>;
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};