import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import path from 'path';
// https://vite.dev/config/
export default defineConfig({
  plugins: [react(),     tailwindcss(),
  ],
  resolve: {
    alias: {
      'react': path.resolve(__dirname, './node_modules/react'),
      'react-dom': path.resolve(__dirname, './node_modules/react-dom'),
    },
  },
  build: {
    outDir:'build',
    sourcemap: true, // Helps with debugging
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Single vendor chunk approach - more reliable but larger initial load
          if (id.includes('node_modules/')) {
            return 'vendor';
          }



          // Application code
          return undefined;
        },
        // Ensure consistent chunk names for better caching
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
      },
    },
    // Other useful build options
    chunkSizeWarningLimit: 1000, // KB
    cssCodeSplit: true,
    minify: 'esbuild', // Default minifier that doesn't require additional dependencies
    // If you want to use Terser instead, install it first:
    // npm install terser --save-dev
    // Then change 'minify' to 'terser' and uncomment below:
    /*
    terserOptions: {
      compress: {
        // Disable console removal in development
        drop_console: process.env.NODE_ENV === 'production',
      },
    },
    */
  },
});