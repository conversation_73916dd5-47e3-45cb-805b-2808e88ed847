// components/Modal.tsx
import React from "react";
import { CircleX } from "lucide-react";

interface ModalProps {
  isOpen: boolean;
  label?: string;
  onClose: () => void;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, label, onClose, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-[#00000052] bg-opacity-50 z-1000 flex items-center justify-center">
      <div className="bg-white pt-4 pb-10 px-6 rounded-3xl min-w-md xs:max-sm:min-w-[200px] xs:max-sm:w-[90%] relative w-full max-w-md">
        <div className="flex justify-between items-center">
          {label && (
            <h2 className="text-[21px] font-semibold tracking-tighter noto-sans">
              {label}
            </h2>
          )}
          <CircleX size={28} onClick={onClose} className="cursor-pointer" />
        </div>
        <div className="mt-4">{children}</div>
      </div>
    </div>
  );
};

export default Modal;
