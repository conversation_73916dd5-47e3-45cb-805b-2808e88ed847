
import AppRoutes from './Routes'
import { I18nextProvider } from "react-i18next";
import i18n from "./i18n";
import Toast from './components/Toast';
import './App.css'
import { Crisp } from 'crisp-sdk-web';
import { useEffect } from 'react';

const CrispChat = () => {
  useEffect(() => {
    // IMPORTANT: Replace "YOUR_CRISP_WEBSITE_ID" with your actual Crisp Website ID.
    // You can find this in your Crisp dashboard under Settings > Workspace Settings > Setup & Integrations.
    Crisp.configure("30492656-69d9-4fed-b6b7-47d51f9ddd4e");

    // Optional: Set user information if you have it (e.g., after user logs in)
    // Crisp.user.setEmail("<EMAIL>");
    // Crisp.user.setNickname("John Doe");
    // Crisp.session.setData({
    //   user_id: "12345",
    //   plan: "premium"
    // });


    return () => {
      // <PERSON>risp doesn't have an explicit 'destroy' method for the widget,
      // but if you were to dynamically remove the script, this is where you'd do it.
      // For most use cases, configuring once is sufficient.
    };
  }, []); // Empty dependency array ensures this runs only once on component mount

  // This component doesn't render any visible UI itself; Crisp handles that.
  return null;
};
function App() {
  return (
    <>
      <I18nextProvider i18n={i18n}>
        <Toast />
        <AppRoutes />
              <CrispChat />
      </I18nextProvider>
    </>
  )
}
export default App