import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import MainLayout from "./layouts/MainLayout";
// import Home from "./pages/Home";
import GroupPosts from "./pages/GroupPosts";
import Publications from "./pages/Publications";
import Entities from "./pages/Entities";
import SettingsEntities from "./pages/SettingsEntities";
import Attributes from "./pages/Attributes";
import Socialmedia from "./pages/Socialmedia";
import { AuthProvider } from "./context/authContext";
import CallbackPage from "./pages/CallbackPage";
import ProtectedRoute from "./components/protectedRoute";
import EntitiesRelatedtoPublication from "./pages/entitiesrelatedtopublication";
import LinkEntitiesToPost from "./pages/linkentitiestopost";
import Login from "./pages/login";
import PowerBIReport from "./pages/PowerBIReport";
import SocialCallback from "./pages/SocialCallback";
import ProfileSettings from "./pages/ProfileSettings";
import AlertScopeConfig from "./pages/AlertScopeConfig";
import AlertMentionsMetrics from "./pages/MetricsConfiguration";
import AlertCharacteristics from "./pages/CharacteristicsConfiguration";
import AllAttributes from "./pages/AllAttributes";
import InstructionPage from "./pages/instructions";
import ContactUsPage from "./pages/contactUsPage";
import MetricsTable from "./pages/Metrics_table";
import SentimentTable from "./pages/sentiment_table";
import MentionTable from "./pages/mention_table";
import UserManagement from "./pages/user_Management";


const AppRoutes: React.FC = () => {
  return (
    <Router>
      <AuthProvider>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/auth/callback" element={<CallbackPage />} />

          {/* <Route path="/dashboard" element={<ProtectedRoute><MainLayout><Home /></MainLayout></ProtectedRoute>} /> */}
          <Route path="/group-posts" element={<ProtectedRoute><MainLayout><GroupPosts /></MainLayout></ProtectedRoute>} />
          <Route path="/publications" element={<ProtectedRoute><MainLayout><Publications /></MainLayout></ProtectedRoute>} />
          <Route path="/entities" element={<ProtectedRoute><MainLayout><Entities /></MainLayout></ProtectedRoute>} />
          <Route path="/settingsentities" element={<ProtectedRoute><MainLayout><SettingsEntities /></MainLayout></ProtectedRoute>} />
          <Route path="/attributes" element={<ProtectedRoute><MainLayout><Attributes /></MainLayout></ProtectedRoute>} />
          <Route path="/Allattributes" element={<ProtectedRoute><MainLayout><AllAttributes /></MainLayout></ProtectedRoute>} />
          <Route path="/socialmedia" element={<ProtectedRoute><MainLayout><Socialmedia /></MainLayout></ProtectedRoute>} />
          <Route path="/analaytics" element={<ProtectedRoute><MainLayout><PowerBIReport /></MainLayout></ProtectedRoute>} />
          <Route path="/instructions" element={<ProtectedRoute><MainLayout><InstructionPage /></MainLayout></ProtectedRoute>} />
          <Route path="/contactUs" element={<ProtectedRoute><MainLayout><ContactUsPage /></MainLayout></ProtectedRoute>} />
          <Route path="/entitiesrelatedtopublication/:id" element={<ProtectedRoute><MainLayout><EntitiesRelatedtoPublication /></MainLayout></ProtectedRoute>} />
          <Route path="/linkentitiestopost/:id" element={<ProtectedRoute><MainLayout><LinkEntitiesToPost /></MainLayout></ProtectedRoute>} />
          <Route path="/auth/callback/:provider" element={<ProtectedRoute><MainLayout><SocialCallback /></MainLayout></ProtectedRoute>} />
          <Route path="/profile-settings" element={<ProtectedRoute><MainLayout><ProfileSettings /></MainLayout></ProtectedRoute>} />
          <Route path="/user-management" element={<ProtectedRoute><MainLayout><UserManagement /></MainLayout></ProtectedRoute>} />
          <Route path="/alerts/scope" element={<ProtectedRoute><MainLayout><AlertScopeConfig /></MainLayout></ProtectedRoute>} />
          <Route path="/alerts/scope/:id" element={<ProtectedRoute><MainLayout><AlertScopeConfig /></MainLayout></ProtectedRoute>} />
          <Route path="/alerts/sentiment" element={<ProtectedRoute><MainLayout><AlertCharacteristics /></MainLayout></ProtectedRoute>} />
          <Route path="/alerts/sentiment/:id" element={<ProtectedRoute><MainLayout><AlertCharacteristics /></MainLayout></ProtectedRoute>} />
          <Route path="/alerts/mentions" element={<ProtectedRoute><MainLayout><AlertMentionsMetrics /></MainLayout></ProtectedRoute>} />
          <Route path="/alerts/mentions/:id" element={<ProtectedRoute><MainLayout><AlertMentionsMetrics /></MainLayout></ProtectedRoute>} />
          <Route path="/alert/Metrics_table"element={<ProtectedRoute><MainLayout><MetricsTable /></MainLayout></ProtectedRoute> }/>
          <Route path="/alerts/sentiment_table"element={<ProtectedRoute><MainLayout> <SentimentTable /></MainLayout></ProtectedRoute>}/>
          <Route path="/alerts/mention_table"element={<ProtectedRoute><MainLayout><MentionTable /></MainLayout></ProtectedRoute>}/>
          <Route path="*" element={<Navigate to="/analaytics" replace />} />
        </Routes>
      </AuthProvider>
    </Router>
  );
};

export default AppRoutes;
