import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import apiService from "../services/apiService";
import Loader from "./loader";
import { CirclePlus, SquarePen } from "lucide-react";
import { GoPencil } from "react-icons/go";
import Modal from "../components/Modal";
import Pagination from "../components/Pagination";
import { Switch } from "@headlessui/react";

interface Entity {
  _id: string;
  EntityId: string;
  Entity: string;
  thumbnail?: string;
  IsDeleted: boolean;
}

function SettingsEntities() {
  const { t } = useTranslation();
  const [data, setData] = useState<Entity[]>([]);
  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [entity, setEntity] = useState("");
  const [editingEntityId, setEditingEntityId] = useState<string | null>(null);
  const [, setSelectedImage] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const [currentUploadingEntityId, setCurrentUploadingEntityId] = useState<
    string | null
  >(null);

  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const totalItems = data.length;
  const pageSize = itemsPerPage;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);
  const paginatedData = data.slice(startIndex, endIndex);
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= Math.ceil(data.length / itemsPerPage)) {
      setCurrentPage(newPage);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await apiService.get("/entities/all");
      console.log("response : ", response);
      setData(response as Entity[]);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  // const handleAddEntity = () => {
  //   setShowForm(true);
  //   setEditingEntityId(null);
  //   setEntity("");
  // };

  const handleEdit = (id: string, entityValue: string) => {
    setEditingEntityId(id);
    setEntity(entityValue);
    setShowForm(true);
  };

  const handleSubmit = async () => {
    if (!entity.trim()) {
      alert(t("settingsEntitie.emptyEntityError"));
      return;
    }

    // Case-insensitive check for duplicate entity names
    const entityLower = entity.trim().toLowerCase();
    const duplicateEntity = data.find(
      (item) =>
        item.Entity.toLowerCase() === entityLower &&
        item.EntityId !== editingEntityId
    );

    if (duplicateEntity) {
      alert(t("settingsEntitie.duplicateEntityError", { entity }));
      return;
    }

    setLoading(true);
    try {
      let response;
      if (editingEntityId) {
        response = await apiService.put(`/entities/${editingEntityId}`, {
          entity,
        });
      } else {
        response = await apiService.post("/entities", { entity });
      }
      if (response) {
        fetchData();
      }
      setShowForm(false);
      setEntity("");
      setEditingEntityId(null);
    } catch (error) {
      console.error("Error saving entity:", error);
      alert(t("settingsEntitie.saveError"));
    } finally {
      setLoading(false);
    }
  };

  // const handleDelete = async (id: string) => {
  //   setLoading(true);
  //   try {
  //     await apiService.delete(`/entities/${id}`);
  //     setData((prevData) => prevData.filter((item) => item.EntityId !== id));
  //   } catch (error) {
  //     console.error("Error deleting item:", error);
  //     alert(t("settingsEntitie.deleteError"));
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleImageClick = (entityId: string) => {
    setCurrentUploadingEntityId(entityId);
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleImageSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];

    if (!file || !currentUploadingEntityId) return;

    if (!file.type.startsWith("image/")) {
      alert(t("settingsEntitie.invalidImageError"));
      return;
    }

    const maxSizeInBytes = 4 * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      alert(t("settingsEntitie.imageSizeError"));
      return;
    }

    await handleUploadImage(currentUploadingEntityId, file);
    setCurrentUploadingEntityId(null);
  };

  const handleUploadImage = async (entityId: string, imageFile: File) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("image", imageFile, imageFile.name);

      const response = await apiService.putimage(
        `/entities/${entityId}/image`,
        formData,
        {
          accept: "*/*",
          "Content-Disposition": `attachment; filename="${imageFile.name}"`,
        }
      );

      if (response) {
        fetchData();
        setSelectedImage(null);
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      alert(t("settingsEntitie.imageUploadError"));
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = async (EntityId: string, currentStatus: boolean) => {
    try {
      // Prepare bulk update payload
      const payload = {
        updates: [
          {
            id: EntityId,
            isDeleted: !currentStatus,
          },
        ],
      };
      await apiService.post(`/entities/bulk-update`, payload);
      const updatedData = data.map((item) =>
        item.EntityId === EntityId
          ? { ...item, IsDeleted: !currentStatus }
          : item
      );
      setData(updatedData);
      // setFilteredData(updatedData); // Update filteredData to reflect toggle changes
    } catch (error) {
      console.error("Error updating toggle:", error);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container-fluid xs:max-sm:px-2">
      <div className="flex justify-between items-center my-4">
        <h1 className="pl-2 font-bold text-xl xs:max-sm:text-lg">
          {t("settingsEntitie.settingsEntitiesTitle")}
        </h1>
        {/* <button
          className="bg-primary font-semibold flex items-center justify-center gap-2 btn text-[#ffffff] py-3 px-4 rounded-xl"
          onClick={handleAddEntity}
          disabled
        >
          {t("settingsEntitie.addOnEntity")}
        </button>
         */}
        {/* <button
          className="bg-gray-300 text-gray-500 cursor-not-allowed font-semibold flex items-center justify-center gap-2 btn py-3 px-4 rounded-xl  hover:bg-gray-300 hover:text-gray-500"
          onClick={(e) => e.preventDefault()}
          disabled
          title="This action is currently disabled"
        >
          {t("settingsEntitie.addOnEntity")}
        </button> */}


      </div>
      <Modal
        label={
          editingEntityId
            ? t("settingsEntitie.editEntity")
            : t("settingsEntitie.addNewKeyLabel")
        }
        isOpen={showForm}
        onClose={() => {
          setShowForm(false);
        }}
      >
        <div className="gap-4 mt-6 w-[85%] mx-auto">
          <input
            className="rounded-xl w-full border outline-none border-gray-300 bg-white px-3 mt-2 py-3.5 text-gray-900 form-control"
            type="text"
            placeholder={t("settingsEntitie.enterNewKeyPlaceholder")}
            value={entity}
            onChange={(e) => setEntity(e.target.value)}
          />
          <button
            className="bg-primary w-full flex items-center py-3.5 mt-4 justify-center gap-2 btn text-white text-md font-extralight rounded-xl"
            onClick={handleSubmit}
          >
            {editingEntityId ? (
              <SquarePen className="font-lighter" size={20} />
            ) : (
              <CirclePlus className="font-lighter" size={20} />
            )}
            {editingEntityId
              ? t("settingsEntitie.update")
              : t("settingsEntitie.addButton")}
          </button>
        </div>
      </Modal>
      <div className="mt-6 bg-gray-100">
        <div className="bg-white rounded-2xl overflow-x-auto xs:max-sm:overflow-x-none">
          <div className="w-full overflow-x-auto xs:max-sm:overflow-x-none">
            <table className="min-w-full xs:max-sm:max-w-[100%] border-collapse text-left">
              <thead>
                <tr className="border-b border-gray-100 font-bold tracking-tight bg-[#F3FAFD] text-sm">
                  <th className="py-4 pl-6 pr-4 xs:max-sm:px-4 xs:max-sm:text-sm text-nowrap">
                    {t("settingsEntitie.imageHeader")}
                  </th>
                  <th className="p-4 xs:max-sm:px-4 xs:max-sm:text-sm">
                    {t("settingsEntitie.entityKeyHeader")}
                  </th>
                  <th className="py-4 pl-4 pr-6  xs:max-sm:px-4 xs:max-sm:text-sm">
                    {t("settingsEntitie.actionsHeader")}
                  </th>
                  <th className="py-4 pl-4 pr-6  xs:max-sm:px-4 xs:max-sm:text-sm">
                    {t("settingsEntitie.deleteHeader")}
                  </th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={5}>
                      <Loader />
                    </td>
                  </tr>
                ) : paginatedData.length > 0 ? (
                  paginatedData.map((item) => {
                    return (
                      <React.Fragment key={item._id}>
                        <tr className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-8 xs:max-sm:px-4">
                            <div className="relative w-16 h-16 xs:max-sm:w-10 xs:max-sm:h-10 group">
                              <img
                                src={`${item.thumbnail}?t=${Date.now()}`}
                                alt={item.Entity || "Thumbnail"}
                                className="w-12 h-12 object-cover rounded-md"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.onerror = null;
                                  target.src =
                                    "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANkAAACUCAMAAAAppnz2AAAAQlBMVEX///+8vb/v8PC1trnKy8zGyMf6+/rj5OS5u77///319fbHx8nHx8u6vsG5u7y8vcHU1dbe3t7q6uu1tbTa2t2+wb80IpWwAAAE80lEQVR4nO2d2ZajIBBAg9qGYBqXMf//q6O4BBewQCBlmvsyL4nhHqiiWOy53SKRSCQSiUQwkF+TY7Hk9XM9nsUrOTZLyRX5F80uh97scWGzFGxGeQ9FT9dIM7Oivl+F+mliRp+A+QEJrKAmZgUL0ywHfKnZ4/H4UrOuzYZmz6uYGY/GaIaAaBbN8BDNohkeolk0w0M0i2Z4iGYfNMtZh/nXsJsl9yrLiiKr6sbwm7jNWPaadjc5+bmbfRexGatSQt/bvTTNTH4dsVmSrTfcOTEYknjNupaV6116SuAjEq9ZQddeguNToxG0ZtX+2Q98xxOrWbPr1Te1Aj4BqVme7Y/Fvg3AQxOkZsou6xJkDXsEUrOaq9VK2CNwmuWKxDgAS49IzTRehMPmNJxmTHfcDww0nGbaiwyXNvvePvtes5xosj6HFfxIzTKN2QtWhOA0083UNIM9AqlZ8lKapcDVJ1KzW6XqNEqAT8BqlpzMH3jNbvf9ypFDl2d4zfYX1TwDX2nDa5bvhBo3uB2F1+x2aym17jG8Zo/+R5NM2kmlvDDaJA5s9hAtBn1Q/HOvCiLu93KS1Wa3RoOasQSuNpInTV1Xbd0kprdhQ5rlP6/kZmhmT0AzVnBKwVu8pwln1on16S2YWjAzIfb7S0vTEz77HwxjlhfjvEvLQL0WyExaJFMaptfCmDF5ZzRQGglixoplCRgk1kKYiRj7lcyCxFoAMxFjv7JZkFjzbzYMxYVYkFjzbsae+4vjLtb81lm+zfJCsVXjPdY8m2k2eyl4r8YOv2ZsnRVluN8a0qsZyzZZcWH26nvN17LGp9mi8tgdkKQxXooa/roXM+2R7KhGmwv22bqkUgxJb7HmzYzpDoqWA9IPvsy0WVGm9FUeezLLX7qsGKTX/JjBYmxSg5fHjWgKLOd4MTMSM+i1Nq0fU3sAjXBvdjiPWaq1lNAaPEd4MBOXpkAhNjNUIwe03UAoP3l+xvpTBljykNSOy+O2a0DZ39wE9ppzM/azs9AEmA2FlrrV7TTC+TggjwRdmxkmjzf9FitEjIyxdpggHZtZixH9jtbi/JOCYs2tGdPdKjqEKxc17fLMGnRL2qlZUhDzEJP6op+yd8zydj2LpO1xGnFpJp5lmhUlymG9tnluL7Z63YIe35dzaHYmxqYGk2Z74tvuPXZU0+QRd2bnYmxq8HYpqnjZIq3yQGYOemxSk8l3e6znqBpxZeZIjIzJf+6LTfKQ1bSH8o7MmHmtqGKMtbfY5l2t+ZPaNOLGjFHwQhOgxudYG2tFFamu15yYJc6G4qA2xpo6xt5q6qWoCzMxQbtFxFp7/Dnezg30YKZ/icUKEWua5CF9slW1+rxZnjodimODeaOYx9YoY+20GXj7zVCtn/c1yUNW25+tz5olJ2tFB0xT9krwpJmHGDOHVn1yXJeb58zYp6UGhmrEZZ85KYJdsFdDnjFLMAzFAb5dip4w85QV7djujdibsdJm+80bm3nN2gxNjE2M2z5zhrQ1c7cec8YQa3O8WZoZH0qEYBlrdmZiHkMTYjOLVbaVGesLus+WVPvIacTm7xE7Xmi6RKohLcwQTdBbaDUdZ5iboUv3S+ZCy9gMudg71kzNGkwl1T7jUtTMjBBx2oLbjNKq7mjFvhPc7BKI/+1hSHNfZiYRza7H3zYrsitShHtJMRKJRCKRSORP8h+ptHV/XdGNDgAAAABJRU5ErkJggg==";
                                }}
                              />
                              <button
                                type="button"
                                onClick={() => handleImageClick(item.EntityId)}
                                className="absolute top-0 right-0 m-1 p-1 bg-white rounded-full shadow-md hover:bg-gray-100 transition-opacity opacity-0 group-hover:opacity-100"
                                title={t("settingsEntitie.changeImage")}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  strokeWidth={2}
                                  stroke="currentColor"
                                  className="w-4 h-4 text-gray-600"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M15.232 5.232l3.536 3.536M9 13l6-6m2.121-2.121a1.5 1.5 0 112.121 2.121L9 17.25H5.25V13L17.121 1.879z"
                                  />
                                </svg>
                              </button>
                              <input
                                type="file"
                                accept="image/*"
                                onChange={handleImageSelect}
                                className="hidden"
                                ref={fileInputRef}
                              />
                            </div>
                          </td>
                          <td className="py-4 px-8 xs:max-sm:text-[13px] xs:max-sm:px-4 text-[15px] text-nowrap">
                            {item.Entity}
                          </td>
                          <td className="py-4 pr-6 pl-4 xs:max-sm:px-4">
                            <div className="flex gap-3 items-center">
                              <button
                                className="px-3 py-3 xs:max-sm:px-1.5 xs:max-sm:py-1.5 xs:max-sm:text-sm xs:max-sm:rounded-md xs:max-sm:mt-2 rounded-xl bg-[#21CE9E1A] cursor-pointer text-[#21CE9E]"
                                onClick={() =>
                                  handleEdit(item.EntityId, item.Entity)
                                }
                              >
                                <GoPencil />
                              </button>
                              {/* <button
                                className="px-3 py-3 xs:max-sm:px-1.5 xs:max-sm:py-1.5 xs:max-sm:text-sm xs:max-sm:rounded-md xs:max-sm:mt-2 rounded-xl cursor-pointer text-[#FB4242] bg-[#FB42421A]"
                                onClick={() => handleDelete(item.EntityId)}
                              >
                                <GoTrash />
                              </button> */}
                            </div>
                          </td>

                          <td className="py-4 pr-6 pl-4 xs:max-sm:px-4">
                            <div
                              title={item.IsDeleted ? t("settingsEntitie.enableTooltip") : t("settingsEntitie.disableTooltip")}
                              className="inline-block"
                            >
                              <Switch
                                checked={item.IsDeleted}
                                onChange={() => handleToggle(item.EntityId, item.IsDeleted)}
                                className={`${item.IsDeleted ? "bg-gray-300" : "bg-primary"
                                  } relative inline-flex h-6 w-11 items-center rounded-full cursor-pointer transition-colors focus:outline-none`}
                              >
                                <span
                                  className={`${item.IsDeleted ? "translate-x-1" : "translate-x-6"
                                    } inline-block h-4 w-4 transform bg-white rounded-full transition-transform`}
                                />
                              </Switch>
                            </div>
                          </td>
                        </tr>
                      </React.Fragment>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan={5} className="text-center py-4">
                      {t("settingsEntitie.noData")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className="mt-4 px-8">
            <Pagination
              currentPage={currentPage}
              totalItems={data.length}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default SettingsEntities;