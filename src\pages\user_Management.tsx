import React, { useEffect, useState } from "react";
import apiService from "../services/apiService";
import Loader from "./loader";
import { GoTrash } from "react-icons/go";
import Pagination from "../components/Pagination";
import Modal from "../components/Modal"; 
import Dropdown from "../components/Dropdown"; 
import { useTranslation } from "react-i18next"; 
import { showErrorToast, showSuccessToast } from "../components/Toast";

interface InfoTooltipProps {
  content: string;
}

const InfoTooltip: React.FC<InfoTooltipProps> = ({ content }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className="relative inline-block ml-2">
      <button
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="text-gray-400 hover:text-gray-600 focus:outline-none"
        aria-label="Information"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z"
            clipRule="evenodd"
          />
        </svg>
      </button>
      {showTooltip && (
        <div className="absolute z-10 w-64 p-2 mt-2 text-sm text-white bg-gray-800 rounded-md shadow-lg left-full ml-1">
          {content}
        </div>
      )}
    </div>
  );
};

interface User {
  Username: string;
  Email: string;
  Role: string;
}

const UserManagement: React.FC = () => {
  const { t } = useTranslation(); // Initialize useTranslation
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [deletingEmail, setDeletingEmail] = useState<string | null>(null);

  // States for pagination
  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const totalItems = users.length;
  const pageSize = itemsPerPage;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);
  const paginatedUsers = users.slice(startIndex, endIndex);

  // States for invite modal
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteData, setInviteData] = useState({
    username: "",
    email: "",
    role: "User", // Changed default to 'User' as 'Admin' might require more permissions
  });

  // Condition for invite button
  const userData = JSON.parse(localStorage.getItem("UserData") || "{}");
  const pkg = userData.pkg?.toLowerCase() || "unknown";
  const role = userData.role?.toLowerCase() || "unknown";
  const isInviteDisabled = pkg === "unknown" || pkg === "free" || pkg === "basic" || role === "user";

  const roleOptions = [
    { value: "Admin", label: "Admin" },
    { value: "User", label: "User" },
  ];

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= Math.ceil(users.length / itemsPerPage)) {
      setCurrentPage(newPage);
    }
  };

  // Fetch all users
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const resp = await apiService.get<User[]>("/user/all");
      setUsers(resp);
    } catch (err) {
      console.error("Error fetching users:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // Delete a user
 const handleDelete = async (email: string) => {
  if (!window.confirm(t("userManagement.confirmDelete", { email }))) return;

  setDeletingEmail(email);

  try {
    const response = await apiService.delete(`/user/${encodeURIComponent(email)}`);
    const { message } = response as { message: string };
    showSuccessToast(message);
    setUsers((u) => u.filter((x) => x.Email !== email));
    // Optionally: show translated success message
    // alert(t("userManagement.deleteSuccess"));
  } catch (err) {
    console.error("Delete failed:", err);
    // Optionally: show translated error message
    // alert(t("userManagement.deleteError"));
  } finally {
    setDeletingEmail(null);
  }
};


  // Handle invite member
 const handleInvite = async () => {
  // Basic validation
  if (!inviteData.username.trim() || !inviteData.email.trim()) {
    showErrorToast(t("userManagement.emptyFieldsError"));
    return;
  }
  if (!/\S+@\S+\.\S+/.test(inviteData.email)) {
    showErrorToast(t("userManagement.invalidEmailError"));
    return;
  }

  try {
    const payload = {
      username: inviteData.username,
      email: inviteData.email,
      role: inviteData.role,
    };
    console.log("Inviting user:", payload);

    const response = await apiService.post("user/invite", payload);
    const { message } = response as { message: string };
    showSuccessToast(message || t("userManagement.inviteSuccess"));      

    setShowInviteModal(false);
    setInviteData({ username: "", email: "", role: "User" });
    fetchUsers(); // Refresh the list
  } catch (error: any) {
    let errorMessage = t("userManagement.inviteError"); // Default fallback
    const err = error?.response?.data || error;

    // Handle specific server error messages
    if (err?.error) {
      switch (err.error) {
        case "Invalid role. Allowed roles are 'Admin' and 'User'.":
          errorMessage = t("userManagement.invalidRoleError");
          break;
        case "Forbidden – Only Admin users can send invitations.":
          errorMessage = t("userManagement.forbiddenInviteError");
          break;
        case "Inviter user not found in the system.":
          errorMessage = t("userManagement.inviterNotFoundError");
          break;
        case "User with this email or username already exists.":
          errorMessage = t("userManagement.userExistsError");
          break;
        case "Failed to invite user.":
          errorMessage = t("userManagement.serverError");
          break;
        default:
          errorMessage = err.error; // Show raw server error if unknown
      }
    }

    showErrorToast(errorMessage);
    console.error("Error inviting user:", error);
  }
};


  return (
    <div className="container-fluid xs:max-sm:px-2">
      <div className="flex justify-between items-center my-4">
        <h1 className="pl-2 font-bold text-xl xs:max-sm:text-lg">
          {t("userManagement.title")}
        </h1>
        <div>
          <button
            onClick={() => {
              if (!isInviteDisabled) setShowInviteModal(true);
            }}
            className={`px-4 py-2 rounded-md transition ${isInviteDisabled
                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                : "bg-primary text-white cursor-pointer"
            }`}
            disabled={isInviteDisabled}
            title={isInviteDisabled ? t("userManagement.inviteDisabledTooltip") : ""}
          >
            {t("userManagement.inviteMembers")}
          </button>
        </div>
      </div>

      <Modal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        label={t("userManagement.inviteMemberModalLabel")}
      >
        <div className="gap-4 mt-6 w-[85%] mx-auto">
          <div className="mb-4">
            <div className="flex items-center">
              <label className="block text-sm font-medium mb-1">
                {t("userManagement.username")}
              </label>
            </div>
            <input
              type="text"
              className="rounded-xl w-full border outline-none border-gray-300 bg-white px-3 mt-2 py-3.5 text-gray-900 form-control"
              value={inviteData.username}
              onChange={(e) => setInviteData({ ...inviteData, username: e.target.value })}
            />
          </div>
          <div className="mb-4">
            <div className="flex items-center">
              <label className="block text-sm font-medium mb-1">
                {t("userManagement.email")}
              </label>
            </div>
            <input
              type="email"
              className="rounded-xl w-full border outline-none border-gray-300 bg-white px-3 mt-2 py-3.5 text-gray-900 form-control"
              value={inviteData.email}
              onChange={(e) => setInviteData({ ...inviteData, email: e.target.value })}
            />
          </div>
          <div className="mb-4">
            <div className="flex items-center">
              <label className="block text-sm font-medium mb-1">
                {t("userManagement.role")}
              </label>
              <InfoTooltip content={t("userManagement.roleTooltip")} />
            </div>
            <Dropdown
              value={inviteData.role}
              className="w-full"
              options={roleOptions}
              onChange={(val: string) => setInviteData({ ...inviteData, role: val })}
            />
          </div>
          <button
            onClick={handleInvite}
            className="bg-primary w-full flex items-center py-3.5 mt-4 justify-center gap-2 btn text-white text-md font-extralight rounded-xl"
          >
            {t("userManagement.sendInvitation")}
          </button>
        </div>
      </Modal>

      <div className="mt-6 bg-gray-100">
        <div className="bg-white rounded-2xl overflow-x-auto xs:max-sm:overflow-x-none">
          <div className="w-full overflow-x-auto xs:max-sm:overflow-x-none">
            <table className="min-w-full xs:max-sm:max-w-[100%] border-collapse text-left">
              <thead>
                <tr className="border-b border-gray-100 font-bold tracking-tight bg-[#F3FAFD] text-sm">
                  <th className="py-4 pl-6 pr-4 xs:max-sm:px-4 xs:max-sm:text-sm text-nowrap">
                    {t("userManagement.usernameHeader")}
                  </th>
                  <th className="p-4 xs:max-sm:px-4 xs:max-sm:text-sm">
                    {t("userManagement.emailHeader")}
                  </th>
                  <th className="py-4 pl-4 pr-6 xs:max-sm:px-4 xs:max-sm:text-sm">
                    {t("userManagement.roleHeader")}
                  </th>
                  <th className="py-4 pl-4 pr-6 xs:max-sm:px-4 xs:max-sm:text-sm">
                    {t("userManagement.actionHeader")}
                  </th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={4}>
                      <Loader />
                    </td>
                  </tr>
                ) : paginatedUsers.length > 0 ? (
                  paginatedUsers.map((u) => (
                    <tr key={u.Email} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-8 xs:max-sm:px-4 xs:max-sm:text-[13px] text-[15px] text-nowrap">
                        {u.Username}
                      </td>
                      <td className="py-4 px-8 xs:max-sm:px-4 xs:max-sm:text-[13px] text-[15px] text-nowrap">
                        {u.Email}
                      </td>
                      <td className="py-4 px-8 xs:max-sm:px-4 xs:max-sm:text-[13px] text-[15px] text-nowrap">
                        {u.Role}
                      </td>
                      <td className="py-4 pr-6 pl-4 xs:max-sm:px-4">
                        <button
                          className="px-3 py-3 xs:max-sm:px-1.5 xs:max-sm:py-1.5 xs:max-sm:text-sm xs:max-sm:rounded-md xs:max-sm:mt-2 rounded-xl cursor-pointer text-[#FB4242] bg-[#FB42421A] flex items-center gap-1"
                          onClick={() => handleDelete(u.Email)}
                          disabled={deletingEmail === u.Email}
                        >
                          <GoTrash />
                          {deletingEmail === u.Email ? "Deleting..." : "Delete"}
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="text-center py-4">
                      {t("userManagement.noUsers")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className="mt-4 px-8">
            <Pagination
              currentPage={currentPage}
              totalItems={users.length}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserManagement;