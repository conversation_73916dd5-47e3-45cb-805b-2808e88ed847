<svg width="43" height="41" viewBox="0 0 43 41" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_di_8008_3079)">
<g clip-path="url(#clip0_8008_3079)">
<rect x="6.3999" y="6.51172" width="24" height="24" rx="6" fill="url(#paint0_linear_8008_3079)" fill-opacity="0.04" shape-rendering="crispEdges"/>
<g style="mix-blend-mode:plus-lighter" opacity="0.5" filter="url(#filter1_f_8008_3079)">
<rect x="11.3999" y="11.5117" width="14" height="14" rx="7" fill="#1275B1"/>
<path d="M16.7092 15.3578C16.7092 15.8251 16.3042 16.2039 15.8045 16.2039C15.3049 16.2039 14.8999 15.8251 14.8999 15.3578C14.8999 14.8905 15.3049 14.5117 15.8045 14.5117C16.3042 14.5117 16.7092 14.8905 16.7092 15.3578Z" fill="white"/>
<path d="M15.0236 16.8258H16.57V21.5117H15.0236V16.8258Z" fill="white"/>
<path d="M19.0597 16.8258H17.5133V21.5117H19.0597C19.0597 21.5117 19.0597 20.0365 19.0597 19.1141C19.0597 18.5605 19.2487 18.0045 20.003 18.0045C20.8554 18.0045 20.8503 18.729 20.8463 19.2903C20.8411 20.0239 20.8535 20.7726 20.8535 21.5117H22.3999V19.0386C22.3868 17.4594 21.9753 16.7318 20.6216 16.7318C19.8176 16.7318 19.3193 17.0967 19.0597 17.427V16.8258Z" fill="white"/>
</g>
<rect x="11.3999" y="11.5117" width="14" height="14" rx="7" fill="#1275B1"/>
<path d="M16.7092 15.3578C16.7092 15.8251 16.3042 16.2039 15.8045 16.2039C15.3049 16.2039 14.8999 15.8251 14.8999 15.3578C14.8999 14.8905 15.3049 14.5117 15.8045 14.5117C16.3042 14.5117 16.7092 14.8905 16.7092 15.3578Z" fill="white"/>
<path d="M15.0236 16.8258H16.57V21.5117H15.0236V16.8258Z" fill="white"/>
<path d="M19.0597 16.8258H17.5133V21.5117H19.0597C19.0597 21.5117 19.0597 20.0365 19.0597 19.1141C19.0597 18.5605 19.2487 18.0045 20.003 18.0045C20.8554 18.0045 20.8503 18.729 20.8463 19.2903C20.8411 20.0239 20.8535 20.7726 20.8535 21.5117H22.3999V19.0386C22.3868 17.4594 21.9753 16.7318 20.6216 16.7318C19.8176 16.7318 19.3193 17.0967 19.0597 17.427V16.8258Z" fill="white"/>
<g filter="url(#filter2_f_8008_3079)">
<ellipse cx="18.6499" cy="30.2617" rx="5.75" ry="3.25" fill="#1A75B0"/>
</g>
</g>
<rect x="6.3999" y="6.51172" width="24" height="24" rx="6" stroke="url(#paint1_linear_8008_3079)" stroke-opacity="0.5" stroke-width="0.4" shape-rendering="crispEdges"/>
</g>
<g style="mix-blend-mode:screen" filter="url(#filter3_f_8008_3079)">
<circle cx="14.8999" cy="15.0117" r="4.5" fill="white" fill-opacity="0.18"/>
</g>
<defs>
<filter id="filter0_di_8008_3079" x="-3.80029" y="-3.6875" width="46.4004" height="44.3984" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8008_3079"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8008_3079" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0509804 0 0 0 0 0.538353 0 0 0 0 0.988235 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_8008_3079"/>
</filter>
<filter id="filter1_f_8008_3079" x="4.3999" y="4.51172" width="28" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3" result="effect1_foregroundBlur_8008_3079"/>
</filter>
<filter id="filter2_f_8008_3079" x="3.8999" y="18.0117" width="29.5" height="24.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.5" result="effect1_foregroundBlur_8008_3079"/>
</filter>
<filter id="filter3_f_8008_3079" x="0.399902" y="0.511719" width="29" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_8008_3079"/>
</filter>
<linearGradient id="paint0_linear_8008_3079" x1="6.3999" y1="6.51172" x2="30.3999" y2="30.5117" gradientUnits="userSpaceOnUse">
<stop stop-color="#F8FBFF"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_8008_3079" x1="6.8999" y1="6.01172" x2="29.6499" y2="30.5117" gradientUnits="userSpaceOnUse">
<stop stop-color="#D8D8D8" stop-opacity="0.05"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<clipPath id="clip0_8008_3079">
<rect x="6.3999" y="6.51172" width="24" height="24" rx="6" fill="white"/>
</clipPath>
</defs>
</svg>
