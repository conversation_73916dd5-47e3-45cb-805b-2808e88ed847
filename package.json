{"name": "twowayanalytics", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.4", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@tailwindcss/vite": "^4.0.6", "antd": "^5.24.2", "axios": "^1.8.1", "crisp-sdk-web": "^1.0.25", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.482.0", "powerbi-client": "^2.23.1", "powerbi-client-react": "^2.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-icons": "^5.4.0", "react-pro-sidebar": "^1.1.0", "react-router-dom": "^7.3.0", "react-select": "^5.10.1", "recharts": "^2.15.1", "tailwindcss": "^4.0.6"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.11", "@types/react": "^19.0.8", "@types/react-datepicker": "^7.0.0", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}