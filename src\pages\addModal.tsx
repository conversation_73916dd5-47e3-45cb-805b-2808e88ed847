import { <PERSON><PERSON><PERSON>, CircleX, Trash2 } from "lucide-react";
import { useState } from "react";
import facebookIcon from "../assets/Facebook.svg";
import instagramIcon from "../assets/Instagram.svg";
import linkedinIcon from "../assets/Linkedin.svg";
import TwitterIcon from "../assets/x.svg";
import YoutubeIcon from "../assets/youtube.svg";
import apiService from "../services/apiService";
import { IoIosSearch } from "react-icons/io";

interface Post {
  PostID: string;
  Thumbnail: string;
  Text: string;
  SocialNetwork: string;
  TimeStamp: string;
  LocalTimeStamp: string;
  Validation: string;
}

interface SelectedPost {
  Posts: Post[];
  PublicationID: string;
}

interface AddModalProps {
  isOpen: boolean;
  selectedPost: SelectedPost;
  data: any;
  onClose: () => void;
  refreshData: () => void;
}

export default function AddModal({
  isOpen,
  onClose,
  selectedPost,
  data,
  refreshData,
}: AddModalProps) {
  if (!isOpen || !selectedPost) return null;

  // State for search functionality
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);

  // Filter unvalidated posts first
  const unvalidatedPosts: Post[] = data
    .filter(
      (item: any) =>
        item.Validation === "Unvalidated" && Array.isArray(item.Posts) &&
        item.Posts.length === 1
    )
    .flatMap((item: any) => item.Posts);

  // Apply search filter to unvalidated posts
  const filteredPosts = unvalidatedPosts.filter((post) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      post.Text.toLowerCase().includes(searchLower) ||
      post.SocialNetwork.toLowerCase().includes(searchLower) ||
      post.LocalTimeStamp.toLowerCase().includes(searchLower) ||
      post.PostID.toLowerCase().includes(searchLower)
    );
  });

  const handleCheckboxChange = (postID: string) => {
    setSelectedPosts((prev) =>
      prev.includes(postID)
        ? prev.filter((id) => id !== postID)
        : [...prev, postID]
    );
  };

  const handleAddChecked = async () => {
    if (!selectedPost?.PublicationID) {
      console.error("Error: No PublicationID found.");
      return;
    }

    if (selectedPosts.length === 0) {
      console.warn("No posts selected.");
      return;
    }

    try {
      await Promise.all(
        selectedPosts.map((postID) => {
          const formattedData = {
            postID,
            targetPublicationID: selectedPost.PublicationID,
          };
          return apiService.put("/publications/move-post", formattedData);
        })
      );
      console.log("All posts moved successfully!");
    } catch (error) {
      console.error("Error moving posts:", error);
    } finally {
      onClose();
      refreshData();
    }
  };

  // Helper function to get social media icons
  const getSocialIcon = (network: string) => {
    switch (network) {
      case "Facebook":
        return facebookIcon;
      case "Instagram":
        return instagramIcon;
      case "linkedin":
        return linkedinIcon;
      case "Twitter":
        return TwitterIcon;
      case "Youtube":
        return YoutubeIcon;
      default:
        return "";
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-[#00000052] bg-opacity-50 z-1000">
      <div className="bg-white pt-4 pb-10 px-6 rounded-3xl shadow-lg max-w-[60%] w-full xs:max-sm:max-w-[90%] max-h-[90vh] overflow-hidden">
        <div className="flex justify-between items-center">
          <h2 className="text-[21px] font-semibold tracking-tight noto-sans">
            Add Post
          </h2>
          <CircleX size={28} onClick={onClose} className="cursor-pointer" />
        </div>

        {/* Search Input */}
        <div className="relative mt-4 mb-4">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <IoIosSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search posts..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex items-end gap-3 justify-between mt-2">
          <button
            className="bg-primary flex items-center py-3 px-4 justify-center gap-2 btn text-white text-md font-extralight rounded-xl"
            onClick={handleAddChecked}
          >
            <CirclePlus className="font-lighter" size={20} /> Add Checked
          </button>
        </div>

        {/* Posts Table */}
        <div className="overflow-x-auto mt-4 max-h-[400px] overflow-y-auto border border-gray-200 rounded-lg scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
          <table className="w-full border-collapse text-left p-4">
            <thead className="sticky top-0 bg-white shadow-md">
              <tr className="border-b border-gray-200 bg-lightblue font-extralight text-md">
                <th className="p-3 font-extralight">Select</th>
                <th className="p-3 font-extralight">Thumbnail</th>
                <th className="p-3 font-extralight">Text</th>
                <th className="p-3 text-nowrap font-extralight">
                  Social Network
                </th>
                <th className="p-3 text-nowrap font-extralight">Time Stamp</th>
                <th className="p-3 font-extralight">Validation</th>
                <th className="p-3 font-extralight">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredPosts.length > 0 ? (
                filteredPosts.map((item) => (
                  <tr
                    key={item.PostID}
                    className="border-b border-gray-100 hover:bg-gray-50"
                  >
                    <td className="p-3">
                      <input
                        type="checkbox"
                        className="form-control peer border border-gray-200 w-[20px] h-[20px]"
                        checked={selectedPosts.includes(item.PostID)}
                        onChange={() => handleCheckboxChange(item.PostID)}
                      />
                    </td>
                    <td className="p-3">
                      <img
                        src={item.Thumbnail}
                        alt="Thumbnail"
                        className="w-16 h-16 rounded-xl"
                      />
                    </td>
                    <td className="p-3 max-w-[80px] overflow-hidden">
                      <p className="truncate"> {item.Text}</p>
                    </td>
                    <td className="p-3">
                      <img
                        src={getSocialIcon(item.SocialNetwork)}
                        alt={item.SocialNetwork}
                        className="h-[60px] w-[60px]"
                      />
                    </td>
                    <td className="p-3 text-gray-500 text-nowrap">
                      {new Date(item.LocalTimeStamp).toLocaleDateString()}
                    </td>
                    <td className="p-3 text-red-500">
                      <p className="bg-red-100 text-center rounded-md py-1 max-w-[100px] text-sm font-extralight">
                        Unvalidated
                      </p>
                    </td>
                    <td className="p-3">
                      <button className="bg-[#FFECEC] flex items-center justify-center btn py-3 px-3 rounded-lg">
                        <Trash2 size={20} className="text-red-500" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="text-center py-4">
                    {searchTerm ? "No matching posts found" : "No unvalidated posts available"}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}