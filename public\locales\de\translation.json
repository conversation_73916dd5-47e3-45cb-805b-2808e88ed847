{"quickSearch": "Schnellsuche...", "logout": "Abmelden", "selectLanguage": "Sprache auswählen", "welcomeMessage": "<PERSON><PERSON><PERSON><PERSON> zurück, <PERSON>", "dashboardDescription": "Messen Sie Ihre Werbe-ROI und analysieren Sie den Website-Traffic.", "dailyPosts": "Tägliche Beiträge", "home": "Startseite", "groupPost": "Gruppenbeiträge", "linkEntities": "Entitäten verknüpfen", "publications": "Veröffentlichungen", "entities": "Entitäten", "settings": "Einstellungen", "alerts": "Alarme", "scope": "Umfang", "sentiment": "Stimmung", "mentions": "Erwähnungen", "profile-settings": "Profileinstellungen", "profile-details": "Profildetails", "metrics": "Metriken", "characteristics": "Eigenschaften", "analytics": "Analyse", "main_dashboard": "Haupt-Dashboard", "settingsEntities": "Einstellungen für Entitäten", "attributes": "Attribute", "allAttributes": "Alle Attribute", "socialMedia": "Soziale Medien", "instructions": "Anweisungen", "contactUs": "Kontakt", "validPosts": "Gültige Beiträge", "invalidPosts": "Ungültige Beiträge", "text": "Text", "createdTime": "Erstellungszeit", "status": "Status", "thumbnail": "Miniaturansicht", "actions": "Aktionen", "published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unpublished": "Unverö<PERSON><PERSON><PERSON>t", "edit": "<PERSON><PERSON><PERSON>", "check": "Überprüfen", "toggle": "Umschalten", "table": {"title": "Benutzerdaten", "columns": {"name": "Name", "email": "E-Mail", "role": "<PERSON><PERSON>", "status": "Status", "actions": "Aktionen"}, "status": {"active": "Aktiv", "inactive": "Inaktiv"}, "actions": {"edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen"}}, "welcome": {"title": "<PERSON><PERSON><PERSON><PERSON> zurück, {{name}}", "description": "Messen Sie Ihren Werbe-ROI und berichten Sie über den Website-Traffic."}, "groupPosts": {"search": "Beiträge Text", "channel": "Plattform", "filters": {"all": "<PERSON><PERSON> Daten", "date_filter": "Datumsbereich", "daily_posts": "Heutige Beiträge", "last_week_posts": "Letzte 7 Tage", "last_month_posts": "Letzte 30 Tage", "custom_date": "Benutzerdefinierter Bereich", "begin": "<PERSON><PERSON><PERSON>", "end": "<PERSON><PERSON>", "type": "Validierungstyp", "button": "<PERSON><PERSON>"}, "table": {"thumbnail": "Miniaturansicht", "columns": {"name": "Beitragstext", "email": "Plattform", "status": "Status", "actions": "<PERSON><PERSON><PERSON><PERSON>"}, "createdTime": "Erstellungsdatum", "view_original": "Originalbeitrag anzeigen", "no_data": "<PERSON><PERSON> ve<PERSON>ü<PERSON>"}, "status": {"all": "Alle", "validated": "Bestätigt", "unvalidated": "<PERSON>cht bestätig<PERSON>", "partially_validated": "Teilweise bestätigt"}}, "publication": {"searchPosts": "Text suchen...", "dateFilter": "Datumsbereich", "text": "Text", "socialMedia": "Plattform", "status": "Status", "createdTime": "Erstellungsdatum", "linkCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON><PERSON>", "thumbnail": "Miniaturansicht", "dailyPosts": "Heutige Beiträge", "lastWeekPosts": "Letzte 7 Tage", "lastMonthPosts": "Letzte 30 Tage", "customDate": "Benutzerdefinierter Bereich", "beginDate": "<PERSON><PERSON><PERSON>", "endDate": "<PERSON><PERSON>", "postType": "Validierungstyp", "filterButton": "<PERSON><PERSON>", "noPublicationsFound": "Keine Publikationen gefunden", "entitiesModalTitle": "Verknüpfte Attribute zur Veröffentlichung", "loading": "Lade...", "noDataAvailable": "<PERSON><PERSON> verfüg<PERSON>.", "entityKey": "Entitätsschlüssel", "entityValue": "Entitätswert", "validationOptions": {"all": "<PERSON><PERSON> Daten", "validated": "Bestätigt", "unvalidated": "<PERSON>cht bestätig<PERSON>", "partiallyValidated": "Teilweise bestätigt"}}, "entity": {"linkEntitiesToPostTitle": "Entitäten", "value": "Wert", "numberOfLinks": "<PERSON> (#)", "actions": "Inhalt anzeigen", "link": "Verknüpfen", "noDataAvailable": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "linkButton": "Entität verknüpfen", "modalTitle": "Entität mit Publikation verknüpfen"}, "dataModal": {"title": "Beiträge im Zusammenhang mit Entität:", "copy": "<PERSON><PERSON><PERSON>", "platform": "Plattform", "timestamp": "Zeitstempel", "noDataAvailable": "<PERSON><PERSON> ve<PERSON>ü<PERSON>"}, "settingsEntitie": {"settingsEntitiesTitle": "Entitäten verwalten", "addOnEntity": "Zu Entität hinzufügen", "addNewKeyLabel": "Neuen Schlüssel hinzufügen", "updateButton": "<PERSON><PERSON><PERSON>", "submitButton": "Bestätigen", "enterNewKeyPlaceholder": "Neuen Schlüssel eingeben", "entityKeyHeader": "Entität", "imageHeader": "Bild", "actionsHeader": "<PERSON><PERSON><PERSON>", "deleteHeader": "Status", "deleteTooltip": "Aktivieren / Deaktivieren", "enableButton": "Aktivieren", "disableButton": "Deaktivieren", "enableTooltip": "<PERSON><PERSON><PERSON>, um diese Entität zu aktivieren", "disableTooltip": "<PERSON><PERSON><PERSON>, um diese Entität zu deaktivieren", "addButton": "Hinzufügen", "update": "Aktualisieren", "editEntity": "Entität bearbeiten", "noData": "<PERSON><PERSON> ve<PERSON>ü<PERSON>"}, "linkEntitie": {"linkEntitiesToPostTitle": "Entitäten mit Beitrag verknüpfen", "linkEntitiesButton": "Entitäten verknüpfen", "cancelButton": "Abbrechen", "playerTitle": "<PERSON><PERSON><PERSON>", "playerName": "<PERSON>"}, "socialmedia": {"socialMediaLinkingTitle": "Verknüpfung sozialer Medien", "youtubeLoginButton": "YouTube verbinden", "youtubeLogoutButton": "<PERSON> trennen", "xLoginButton": "X (Twitter) verbinden", "xLogoutButton": "X (Twitter) trennen", "facebookLoginButton": "<PERSON>a verbinden", "facebookLogoutButton": "<PERSON><PERSON> trennen", "loggingOut": "Abmeldung läuft...", "permissions": "Berechtigungen:", "youtubePermissions": "Zugriff auf Kanalinformationen, Video-Uploads und Analysen.", "xPermissions": "Profilzugriff und Veröffentlichen von Beiträgen auf Ihrem X-Konto.", "facebookPermissions": "Seiten verwalten, Beiträge veröffentlichen und Seitenstatistiken anzeigen."}, "SettingsAttributes": {"title": "Attributverwaltung", "addAttribute": "Attribut hinzufügen", "enterAttribute": "Attribut eingeben", "selectEntity": "Ausgewählte Entität", "allEntities": "Alle Entitäten", "noDataAvailable": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "attributeSearchPlaceholder": "Attribute suchen...", "save": "Speichern", "update": "Aktualisieren", "delete": "Löschen", "entityKey": "Entität", "entityName": "Attribute", "image": "Bild", "actions": "<PERSON><PERSON><PERSON><PERSON>", "enableTooltip": "<PERSON><PERSON><PERSON>, um dieses Attribut zu aktivieren", "disableTooltip": "<PERSON><PERSON><PERSON>, um dieses Attribut zu deaktivieren", "errorFetchingData": "Fehler beim Abrufen der Daten", "errorSavingAttribute": "Fehler beim Speichern des Attributs", "errorDeletingAttribute": "Fehler beim Löschen des Attributs", "updateAttribute": "Attribut aktualisieren", "confirmDelete": "Möchten Sie dieses Attribut wirklich löschen?"}, "profilesettings": {"profileSettings": "Profileinstellungen", "timezone": "Zeitzone", "timezoneTooltip": "Wählen Sie Ihre lokale Zeitzone für die korrekte Zeitanzeige in der Anwendung", "targetLanguage": "<PERSON><PERSON><PERSON>", "targetLanguageTooltip": "Legen Sie Ihre bevorzugte Sprache für die Anzeige und Übersetzung von Inhalten fest", "minCommentLength": "Minimale Kommentarlänge", "minCommentLengthTooltip": "Legen Sie die minimale Zeichenanzahl fest, damit ein Kommentar gültig ist", "maxCommentLength": "Maximale Kommentarlänge", "maxCommentLengthTooltip": "Legen Sie die maximale Anzahl an Zeichen für Kommentare fest (0 bedeutet keine Begrenzung)", "saveSettings": "Einstellungen speichern", "saving": "Speichern...", "inviteMembers": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>n", "inviteMemberModalLabel": "Neues Mitglied e<PERSON>laden", "username": "<PERSON><PERSON><PERSON><PERSON>", "usernameTooltip": "Geben Sie den Benutzernamen der einzuladenden Person ein", "email": "E-Mail", "emailTooltip": "<PERSON><PERSON><PERSON> Sie die E-Mail-Adresse ein, an die die Einladung gesendet wird", "role": "<PERSON><PERSON>", "roleTooltip": "Wählen Sie die Berechtigungsstufe für den eingeladenen Benutzer aus", "sendInvitation": "Einladung senden", "alertSettings": "Alarmeinstellungen", "enableAlerts": "Alarme aktivieren", "shiftDetectionAlert": "Verschiebungserkennungsalarm", "shiftDetectionTooltip": "Benachrichtigungen bei signifikanten Metrikverschiebungen erhalten", "impactDetectionAlert": "Auswirkungserkennungsalarm", "impactDetectionTooltip": "Benachrichtigungen bei Ereignissen mit großer Auswirkung erhalten", "notificationChannel": "Benachrichtigungskanal", "notificationChannelTooltip": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> au<PERSON>, wie Sie Benachrichtigungen erhalten möchten", "slack": "<PERSON><PERSON>ck", "webhook": "Webhook", "sentimentRange": "Neutralen Sentimentbereich anpassen (in %)", "sentimentRangeTooltip": "Wählen Sie den neutralen Sentimentbereich (zwischen negativ und positiv).", "min": "Min", "max": "Max", "negativeSentiment": "Negatives Sentiment", "neutralSentiment": "Neutrales Sentiment", "positiveSentiment": "Positives Sentiment"}, "alertConfig": {"newAlertConfiguration": "Neue Alarmkonfiguration", "editAlertConfiguration": "Alarmkonfiguration bearbeiten", "alertConfiguration": "Alarmkonfiguration", "alertScope": "Alarmbereich", "allContent": "Alle Inhalte", "entity": "Entität", "content": "Inhalt", "entityType": "Entitätstyp", "entityAttribute": "Entitätsattribut", "contentChannel": "Inhaltskanal", "postDate": "Veröffentlichungsdatum", "allDates": "<PERSON><PERSON> Daten", "postText": "Beitragstext", "metrics": "Metriken", "metric": "<PERSON><PERSON>", "comments": "Kommentare", "likes": "Gefällt mir", "shares": "Get<PERSON><PERSON>", "views": "Auf<PERSON><PERSON>", "engagements": "Interaktionen", "threshold": "Schwellenwert", "alertCharacteristics": "Alarmmerkmale", "duration": "<PERSON><PERSON>", "noDuration": "<PERSON><PERSON>", "1h": "1 Stunde", "2h": "2 Stunden", "4h": "4 Stunden", "12h": "12 Stunden", "1d": "1 Tag", "2d": "2 Tage", "7d": "7 Tage", "30d": "30 Tage", "repeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "Startdatum", "endDate": "Enddatum", "selectDate": "Da<PERSON> ausw<PERSON>en", "saveAlert": "Alarm speichern", "allChannels": "Alle Kanäle", "saveEntity": "Entität speichern", "saveMetric": "<PERSON><PERSON>", "actions": "Aktionen", "remove": "Entfernen", "XTwitter": "X (Twitter)", "facebook": "Facebook", "instagram": "Instagram", "youtube": "YouTube", "player": "<PERSON><PERSON><PERSON>", "campaign": "<PERSON><PERSON><PERSON><PERSON>", "product": "Produkt", "commentCount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trackedMentions": "Verfolgte Erwähnungen", "enterTrackedWords": "Geben Sie verfolgte Wörter ein (z. B. Produktnamen)", "wordCount": "<PERSON><PERSON><PERSON><PERSON>", "trackedWords": "Verfolgte Wörter (optional)", "sentimentScore": "St<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (%)", "alertType": "Alarmtyp", "above": "<PERSON><PERSON>", "below": "Unter", "optionalValue": "Optionaler Wert (muss >10 sein)"}, "alerts-tablet": {"date_filter": "Datumsfilter", "daily_posts": "Tägliche Alarme", "last_week_posts": "Alarme der letzten Woche", "last_month_posts": "Alarme des letzten Monats", "custom_date": "Benutzerdefiniertes Datum", "begin": "Startdatum", "end": "Enddatum", "scope": "Umfang", "alert_count": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "button": "Filter", "Search Posts": "<PERSON><PERSON><PERSON> suchen", "thumbnail": "Miniaturansicht", "content": "Inhalt", "channel": "<PERSON><PERSON>", "endDate": "Enddatum", "metricType": "Metriktyp", "metrics": "Metriken", "status": "Status", "actions": "Aktionen", "Add Sentiments": "Stimmungen hinzufügen", "Add Metrics": "<PERSON><PERSON><PERSON> hi<PERSON>uf<PERSON>", "Add Mention": "Erwähnung hinzufügen", "enableTooltip": "<PERSON><PERSON><PERSON>, um diesen Alarm zu aktivieren", "disableTooltip": "<PERSON><PERSON><PERSON>, um diesen Alarm zu deaktivieren"}, "userManagement": {"title": "Benutzerverwaltung", "inviteMembers": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>n", "inviteDisabledTooltip": "Diese Funktion ist in Ihrem aktuellen Tarif nicht verfügbar", "inviteMemberModalLabel": "Neues Mitglied e<PERSON>laden", "username": "<PERSON><PERSON><PERSON><PERSON>", "email": "E-Mail", "role": "<PERSON><PERSON>", "roleTooltip": "<PERSON>sen Sie dem neuen Mitglied eine Rolle zu. Admin hat vollständige Kontrolle, Benutzer hat eingeschränkten Zugriff.", "sendInvitation": "Einladung senden", "usernameHeader": "<PERSON><PERSON><PERSON><PERSON>", "emailHeader": "E-Mail", "roleHeader": "<PERSON><PERSON>", "actionHeader": "Aktion", "noUsers": "<PERSON><PERSON> gefunden.", "emptyFieldsError": "Benutzername und E-Mail dürfen nicht leer sein.", "invalidEmailError": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein.", "inviteSuccess": "Benutzer erfolgreich e<PERSON>laden!", "inviteError": "Fehler beim Einladen des Benutzers. Bitte versuchen Sie es erneut.", "invalidRoleError": "Ungültige Rolle. Zulässige Rollen sind 'Admin' und 'Benutzer'.", "forbiddenInviteError": "Nur Admins können Einladungen versenden.", "inviterNotFoundError": "<PERSON><PERSON><PERSON>er wurde im System nicht gefunden.", "userExistsError": "Ein Benutzer mit dieser E-Mail oder diesem Benutzernamen existiert bereits.", "serverError": "Fehler beim Einladen des Benutzers. Bitte versuchen Sie es später erneut.", "confirmDelete": "Sind <PERSON> sicher, dass Si<PERSON> diesen Benutzer löschen möchten?"}}