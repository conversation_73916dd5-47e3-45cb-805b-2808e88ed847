import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Dropdown from "../components/Dropdown";
import Modal from "../components/Modal";
import Slider from '@mui/material/Slider';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import apiService from "../services/apiService";
import { showErrorToast, showSuccessToast } from "../components/Toast";
// import Switch from '@mui/material/Switch';
// import FormControlLabel from '@mui/material/FormControlLabel';
// import FormGroup from '@mui/material/FormGroup';
// import FormControl from '@mui/material/FormControl';

interface InfoTooltipProps {
  content: string;
}

const InfoTooltip: React.FC<InfoTooltipProps> = ({ content }) => {
  const [showTooltip, setShowTooltip] = useState(false);


  return (
    <div className="relative inline-block ml-2">
      <button
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="text-gray-400 hover:text-gray-600 focus:outline-none"
        aria-label="Information"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z"
            clipRule="evenodd"
          />
        </svg>
      </button>
      {showTooltip && (
        <div className="absolute z-10 w-64 p-2 mt-2 text-sm text-white bg-gray-800 rounded-md shadow-lg left-full ml-1">
          {content}
        </div>
      )}
    </div>
  );
};

interface FormDetails {
  timezone: string;
  targetLanguage: string;
  minCommentLength: string;
  maxCommentLength: string;
  sentimentRange: [number, number];
  shiftDetectionAlert: boolean;
  impactDetectionAlert: boolean;
  notificationChannel: string;
}

const ProfileSettings = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const userData = JSON.parse(localStorage.getItem("UserData") || "{}");
  const pkg = userData.pkg?.toLowerCase() || "unknown";
  const role = userData.role?.toLowerCase() || "unknown";
  const isInviteDisabled = pkg === "unknown" || pkg === "free" || pkg === "basic" || role === "user";


  const [formDetails, setFormDetails] = useState<FormDetails>({
    timezone: "",
    targetLanguage: "en",
    minCommentLength: "0",
    maxCommentLength: "1000",
    sentimentRange: [35, 65],
    shiftDetectionAlert: false,
    impactDetectionAlert: false,
    notificationChannel: "email",
  });

  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteData, setInviteData] = useState({
    username: "",
    email: "",
    role: "Admin",
  });

  const timezoneOptions = [
    { value: "UTC", label: "(UTC+00:00) Coordinated Universal Time" },
    { value: "EST", label: "(UTC-05:00) Eastern Time" },
    { value: "PST", label: "(UTC-08:00) Pacific Time" },
    { value: "CET", label: "(UTC+01:00) Central European Time" },
    { value: "IST", label: "(UTC+05:30) India Standard Time" },
  ];

  const languageOptions = [
    { value: "en", label: "English" },
    { value: "es", label: "Spanish" },
    { value: "fr", label: "French" },
    { value: "de", label: "German" },
    { value: "zh", label: "Chinese" },
  ];

  const roleOptions = [
    { value: "Admin", label: "Admin" },
    { value: "User", label: "User" },
  ];

  // const notificationChannelOptions = [
  //   { value: "email", label: t("profilesettings.email") },
  //   { value: "slack", label: t("profilesettings.slack") },
  //   { value: "webhook", label: t("profilesettings.webhook") },
  // ];

  const minSentiment = 10;
  const maxSentiment = 90;

  const handleSentimentChange = (_event: Event, newValue: number | number[]) => {
    setFormDetails({
      ...formDetails,
      sentimentRange: newValue as [number, number],
    });
  };

  // const handleAlertToggle = (field: keyof FormDetails) => (event: React.ChangeEvent<HTMLInputElement>) => {
  //   setFormDetails({
  //     ...formDetails,
  //     [field]: event.target.checked,
  //   });
  // };

  const handleInputChange = (field: keyof FormDetails) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormDetails({
      ...formDetails,
      [field]: e.target.value,
    });
  };

  const handleDropdownChange = (field: keyof FormDetails) => (val: string) => {
    setFormDetails({
      ...formDetails,
      [field]: val,
    });
  };

  // const languageMap: Record<string, string> = {
  //   en: "English",
  //   fr: "French",
  //   de: "German",
  //   es: "Spanish"
  // };

  // const capitalize = (str:any) => str.charAt(0).toUpperCase() + str.slice(1);


  // const handleSaveSettings = () => {
  //   const transformedSettings = {
  //     timezone: formDetails.timezone,
  //     targetLanguage: languageMap[formDetails.targetLanguage] || formDetails.targetLanguage,
  //     minCommentLength: Number(formDetails.minCommentLength),
  //     maxCommentLength: Number(formDetails.maxCommentLength),
  //     neutralSentimentMin: formDetails.sentimentRange[0],
  //     neutralSentimentMax: formDetails.sentimentRange[1],
  //     shiftDetectionAlert: formDetails.shiftDetectionAlert,
  //     impactDetectionAlert: formDetails.impactDetectionAlert,
  //     notificationChannel: capitalize(formDetails.notificationChannel)
  //   };

  //   console.log("Saved settings:", transformedSettings);


  //   // Add your save logic here
  // };

  const decapitalize = (str: string) =>
    typeof str === "string" && str.length > 0
      ? str.charAt(0).toLowerCase() + str.slice(1)
      : str;

  const languageCodeMap: Record<string, string> = {
    English: "en",
    Spanish: "es",
    French: "fr",
    German: "de",
    Chinese: "zh",
  };

  const fetchSettings = async () => {
    try {
      const response = await apiService.get<any>("user/profile-settings");
      const settings = response;
      console.log("✅ Fetched profile settings:", settings);

      setFormDetails({
        timezone: settings.timezone || "",
        targetLanguage: languageCodeMap[settings.targetLanguage] || "en",
        minCommentLength: String(settings.minCommentLength || 0),
        maxCommentLength: String(settings.maxCommentLength || 1000),
        sentimentRange: [
          settings.neutralSentimentMin || 35,
          settings.neutralSentimentMax || 65,
        ],
        shiftDetectionAlert: settings.shiftDetectionAlert || false,
        impactDetectionAlert: settings.impactDetectionAlert || false,
        notificationChannel: decapitalize(settings.notificationChannel) || "email",
      });
    } catch (error) {
      console.error("❌ Error fetching profile settings:", error);
    }
  }


  // Inside your component
  const handleInvite = async () => {
    try {
      const payload = {
        username: inviteData.username,
        email: inviteData.email,
        role: inviteData.role,
      };
      console.log("Inviting user:", payload);

      const response = await apiService.post("user/invite", payload);
      const { message } = response as { message: string };
      showSuccessToast(message || t("userManagement.inviteSuccess"));

      setShowInviteModal(false);
      setInviteData({ username: "", email: "", role: "User" });
    } catch (error: any) {
      let errorMessage = t("userManagement.inviteError"); // Default fallback
      const err = error?.response?.data || error;

      // Handle specific server error messages
      if (err?.error) {
        switch (err.error) {
          case "Invalid role. Allowed roles are 'Admin' and 'User'.":
            errorMessage = t("userManagement.invalidRoleError");
            break;
          case "Forbidden – Only Admin users can send invitations.":
            errorMessage = t("userManagement.forbiddenInviteError");
            break;
          case "Inviter user not found in the system.":
            errorMessage = t("userManagement.inviterNotFoundError");
            break;
          case "User with this email or username already exists.":
            errorMessage = t("userManagement.userExistsError");
            break;
          case "Failed to invite user.":
            errorMessage = t("userManagement.serverError");
            break;
          default:
            errorMessage = err.error; // Show raw server error if unknown
        }
      }

      showErrorToast(errorMessage);
      console.error("Error inviting user:", error);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const handleSaveSettings = async () => {
    setLoading(true);

    const languageMap: Record<string, string> = {
      en: "English",
      fr: "French",
      de: "German",
      es: "Spanish",
    };

    const capitalize = (str: string) =>
      typeof str === "string" && str.length > 0
        ? str.charAt(0).toUpperCase() + str.slice(1)
        : str;

    const transformedSettings = {
      timezone: formDetails.timezone,
      targetLanguage:
        languageMap[formDetails.targetLanguage] || formDetails.targetLanguage,
      minCommentLength: Number(formDetails.minCommentLength),
      maxCommentLength: Number(formDetails.maxCommentLength),
      neutralSentimentMin: formDetails.sentimentRange?.[0] ?? 0,
      neutralSentimentMax: formDetails.sentimentRange?.[1] ?? 100,
      shiftDetectionAlert: !!formDetails.shiftDetectionAlert,
      impactDetectionAlert: !!formDetails.impactDetectionAlert,
      notificationChannel: capitalize(formDetails.notificationChannel),
    };

    try {
      await apiService.post("user/profile-settings", transformedSettings);
      console.log("✅ Profile settings saved:", transformedSettings);
      // Optional: toast.success("Settings saved successfully");
    } catch (error) {
      console.error("❌ Error saving profile settings:", error);
      // Optional: toast.error("Failed to save settings");
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="bg-white rounded-3xl shadow pt-4 xs:max-sm:mt-4">
      <div className="p-6 max-w-3xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">{t("profilesettings.profileSettings")}</h1>

        <div className="space-y-6">
          {/* Timezone */}
          <div>
            <div className="flex items-center">
              <label className="font-medium block mb-2">{t("profilesettings.timezone")}</label>
              <InfoTooltip content={t("profilesettings.timezoneTooltip")} />
            </div>
            <Dropdown
              value={formDetails.timezone}
              className="w-full"
              options={timezoneOptions}
              onChange={handleDropdownChange('timezone')}
            />
          </div>

          {/* Language */}
          <div>
            <div className="flex items-center">
              <label className="font-medium block mb-2">{t("profilesettings.targetLanguage")}</label>
              <InfoTooltip content={t("profilesettings.targetLanguageTooltip")} />
            </div>
            <Dropdown
              value={formDetails.targetLanguage}
              className="w-full"
              options={languageOptions}
              onChange={handleDropdownChange('targetLanguage')}
            />
          </div>

          {/* Comments Length */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="flex items-center">
                <label className="font-medium block mb-2">{t("profilesettings.minCommentLength")}</label>
                <InfoTooltip content={t("profilesettings.minCommentLengthTooltip")} />
              </div>
              <input
                type="number"
                className="py-3 px-4 border w-full border-gray-300 outline-none rounded-md"
                value={formDetails.minCommentLength}
                onChange={handleInputChange('minCommentLength')}
              />
            </div>
            <div>
              <div className="flex items-center">
                <label className="font-medium block mb-2">{t("profilesettings.maxCommentLength")}</label>
                <InfoTooltip content={t("profilesettings.maxCommentLengthTooltip")} />
              </div>
              <input
                type="number"
                className="py-3 px-4 border w-full border-gray-300 outline-none rounded-md"
                value={formDetails.maxCommentLength}
                onChange={handleInputChange('maxCommentLength')}
              />
            </div>
          </div>

          {/* Sentiment Range */}
          <div>
            <div className="flex items-center">
              <label className="font-medium block mb-2">{t("profilesettings.sentimentRange")}</label>
              <InfoTooltip content={t("profilesettings.sentimentRangeTooltip")} />
            </div>
            <Box sx={{ width: '100%', px: 1 }}>
              <Slider
                value={formDetails.sentimentRange}
                onChange={handleSentimentChange}
                valueLabelDisplay="auto"
                min={minSentiment}
                max={maxSentiment}
                disableSwap
              />
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  {t("profilesettings.min")}: {formDetails.sentimentRange[0]}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t("profilesettings.max")}: {formDetails.sentimentRange[1]}%
                </Typography>
              </Box>

              {/* Dynamic Sentiment Ranges */}
              {/* <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="error" fontWeight="bold">
                  {t("profilesettings.negativeSentiment")}: <span style={{ color: 'blue' }}>0% to {formDetails.sentimentRange[0]}%</span>
                </Typography>
                <Typography variant="body2" color="warning.main" fontWeight="bold">
                  {t("profilesettings.neutralSentiment")}: <span style={{ color: 'blue' }}>{formDetails.sentimentRange[0]}% to {formDetails.sentimentRange[1]}%</span>
                </Typography>
                <Typography variant="body2" color="success.main" fontWeight="bold">
                  {t("profilesettings.positiveSentiment")}: <span style={{ color: 'blue' }}>{formDetails.sentimentRange[1]}% to 100%</span>
                </Typography>
              </Box> */}

              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="error" fontWeight="bold">
                  {t("profilesettings.negativeSentiment")}:
                  <span
                    style={{
                      marginLeft: 8,
                      padding: "4px 8px",
                      borderRadius: 8,
                      backgroundColor: "#fdecea", // light red background
                      color: "#d32f2f", // error color
                      fontWeight: 500,
                      fontSize: "0.85rem",
                    }}
                  >
                    0% to {formDetails.sentimentRange[0]}%
                  </span>
                </Typography>

                <Typography variant="body2" color="warning.main" fontWeight="bold" sx={{ mt: 1 }}>
                  {t("profilesettings.neutralSentiment")}:
                  <span
                    style={{
                      marginLeft: 8,
                      padding: "4px 8px",
                      borderRadius: 8,
                      backgroundColor: "#fff8e1", // light yellow background
                      color: "#ed6c02", // warning color
                      fontWeight: 500,
                      fontSize: "0.85rem",
                    }}
                  >
                    {formDetails.sentimentRange[0]}% to {formDetails.sentimentRange[1]}%
                  </span>
                </Typography>

                <Typography variant="body2" color="success.main" fontWeight="bold" sx={{ mt: 1 }}>
                  {t("profilesettings.positiveSentiment")}:
                  <span
                    style={{
                      marginLeft: 8,
                      padding: "4px 8px",
                      borderRadius: 8,
                      backgroundColor: "#e8f5e9", // light green background
                      color: "#2e7d32", // success color
                      fontWeight: 500,
                      fontSize: "0.85rem",
                    }}
                  >
                    {formDetails.sentimentRange[1]}% to 100%
                  </span>
                </Typography>
              </Box>

            </Box>
          </div>

          {/* Alert Settings Section */}
          {/* <div className="border-t border-gray-200 pt-6">
          <h2 className="text-lg font-medium mb-4">{t("profilesettings.alertSettings")}</h2>
          
          <FormControl component="fieldset" fullWidth>
            <FormGroup>
              {/* Alert Type Toggles 
              <FormControlLabel
                control={
                  <Switch
                    checked={formDetails.shiftDetectionAlert}
                    onChange={handleAlertToggle('shiftDetectionAlert')}
                    color="primary"
                  />
                }
                label={
                  <div className="flex items-center">
                    <span>{t("profilesettings.shiftDetectionAlert")}</span>
                    <InfoTooltip content={t("profilesettings.shiftDetectionTooltip")} />
                  </div>
                }
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={formDetails.impactDetectionAlert}
                    onChange={handleAlertToggle('impactDetectionAlert')}
                    color="primary"
                  />
                }
                label={
                  <div className="flex items-center">
                    <span>{t("profilesettings.impactDetectionAlert")}</span>
                    <InfoTooltip content={t("profilesettings.impactDetectionTooltip")} />
                  </div>
                }
              />

              {/* Notification Channel 
              <div className="mt-4">
                <div className="flex items-center mb-2">
                  <label className="font-medium block">{t("profilesettings.notificationChannel")}</label>
                  <InfoTooltip content={t("profilesettings.notificationChannelTooltip")} />
                </div>
                <Dropdown
                  value={formDetails.notificationChannel}
                  className="w-full"
                  options={notificationChannelOptions}
                  onChange={handleDropdownChange('notificationChannel')}
                />
              </div>
            </FormGroup>
          </FormControl>
        </div> */}

          {/* Save & Invite */}
          {/* <button
            className="px-4 py-2 bg-primary text-white mt-6 w-full rounded-md hover:bg-primary-dark transition cursor-pointer"
            onClick={handleSaveSettings}
          >
            {t("profilesettings.saveSettings")}
          </button> */}

          <button
            className={`px-4 py-2 mt-6 w-full rounded-md transition cursor-pointer ${loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-primary hover:bg-primary-dark text-white'
              }`}
            onClick={handleSaveSettings}
            disabled={loading}
          >
            {loading ? t("profilesettings.saving") : t("profilesettings.saveSettings")}
          </button>

          {/* <div>
          <button
            onClick={() => setShowInviteModal(true)}
            className="px-4 py-2 bg-primary text-white rounded-md transition cursor-pointer"
          >
            {t("profilesettings.inviteMembers")}
          </button>
        </div> */}

          <div>
            <button
              onClick={() => {
                if (!isInviteDisabled) setShowInviteModal(true);
              }}
              className={`px-4 py-2 rounded-md transition ${isInviteDisabled
                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                : "bg-primary text-white cursor-pointer"
                }`}
              disabled={isInviteDisabled}
              title={isInviteDisabled ? "This feature is not available in your current plan" : ""}
            >
              {t("profilesettings.inviteMembers")}
            </button>
          </div>
        </div>

        <Modal
          isOpen={showInviteModal}
          onClose={() => setShowInviteModal(false)}
          label={t("profilesettings.inviteMemberModalLabel")}
        >
          <div className="space-y-4">
            <div>
              <div className="flex items-center">
                <label className="block text-sm font-medium mb-1">{t("profilesettings.username")}</label>
              </div>
              <input
                type="text"
                className="w-full p-2 border rounded"
                value={inviteData.username}
                onChange={(e) => setInviteData({ ...inviteData, username: e.target.value })}
              />
            </div>
            <div>
              <div className="flex items-center">
                <label className="block text-sm font-medium mb-1">{t("profilesettings.email")}</label>
              </div>
              <input
                type="email"
                className="w-full p-2 border rounded"
                value={inviteData.email}
                onChange={(e) => setInviteData({ ...inviteData, email: e.target.value })}
              />
            </div>
            <div>
              <div className="flex items-center">
                <label className="block text-sm font-medium mb-1">{t("profilesettings.role")}</label>
                <InfoTooltip content={t("profilesettings.roleTooltip")} />
              </div>
              <Dropdown
                value={inviteData.role}
                className="w-full"
                options={roleOptions}
                onChange={(val: string) => setInviteData({ ...inviteData, role: val })}
              />
            </div>
            <button
              onClick={() => {
                handleInvite()
                // setShowInviteModal(false);
                // setInviteData({ username: "", email: "", role: "member" });
              }}
              className="w-full py-2 bg-primary text-white rounded cursor-pointer"
            >
              {t("profilesettings.sendInvitation")}
            </button>
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default ProfileSettings;