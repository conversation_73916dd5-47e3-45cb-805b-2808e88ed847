import { CircleX } from "lucide-react";
import facebookIcon from "../assets/Facebook.svg";
import instagramIcon from "../assets/Instagram.svg";
import linkedinIcon from "../assets/Linkedin.svg";
import TwitterIcon from "../assets/x.svg";
import YoutubeIcon from "../assets/youtube.svg";
import apiService from "../services/apiService";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Loader from "./loader";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  attributeId: string | null;
  attributeKey: string | null;
}

function DataModal({
  isOpen,
  onClose,
  attributeId,
  attributeKey,
}: ModalProps) {
  const { t } = useTranslation();
  const [publications, setPublications] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await apiService.get<any>(`/attribute-associations/attribute-publication-association?attributeId=${attributeId}`);
      setPublications(response.publications || []);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
    setLoading(false);
  }

  useEffect(() => {
    if (isOpen && attributeId) {
      fetchData();
    }
  }, [isOpen, attributeId]);

  if (!isOpen || !attributeId) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center bg-[#00000052] bg-opacity-50 z-[1000]"
      onClick={onClose}
    >
      <div
        className="bg-white pt-4 pb-10 px-6 rounded-3xl shadow-lg max-w-[60%] w-full xs:max-sm:w-[90%] xs:max-sm:max-w-[90%]"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center">
          <h2 className="text-[21px] font-semibold tracking-tight noto-sans">
            {t("dataModal.title")} {attributeKey || "N/A"}
          </h2>
          <CircleX size={28} onClick={onClose} className="cursor-pointer" />
        </div>

        <div className="overflow-x-auto scrollbar-hidden mt-6 rounded-xl">
          <div className="min-w-[768px] max-h-[300px] overflow-y-auto">
            <table className="w-full border-collapse text-left border border-gray-200 p-4">
              <thead>
                <tr className="border-b border-gray-200 bg-lightblue font-normal text-md">
                  <th className="p-3 font-extralight">{t("dataModal.copy")}</th>
                  <th className="p-3 font-extralight">{t("dataModal.platform")}</th>
                  <th className="p-3 font-extralight">{t("dataModal.timestamp")}</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={6} className="py-4">
                      <Loader />
                    </td>
                  </tr>
                ) :
                  publications.length > 0 ? publications.map((postItem: any) => (
                    <tr key={postItem.PostID} className="border-b border-gray-200 hover:bg-gray-50">
                      <td className="p-3 sm:min-w-[100px] max-w-xs">
                        <p className="truncate">
                          {postItem.Text}
                        </p>
                      </td>
                      <td className="p-3">
                        {(() => {
                          let icon;
                          switch (postItem.SocialNetwork) {
                            case "Facebook":
                              icon = facebookIcon;
                              break;
                            case "Instagram":
                              icon = instagramIcon;
                              break;
                            case "LinkedIn":
                              icon = linkedinIcon;
                              break;
                            case "Twitter":
                              icon = TwitterIcon;
                              break;
                            case "Youtube":
                              icon = YoutubeIcon;
                              break;
                            default:
                              return null;
                          }
                          return icon ? (
                            <img
                              src={icon}
                              alt={postItem.SocialNetwork}
                              className="h-[60px] w-[60px]"
                            />
                          ) : null;
                        })()}
                      </td>
                      <td className="p-3">{postItem.LocalTimeStamp}</td>
                    </tr>
                  )) : <tr>
                    <td colSpan={6} className="text-center py-4">
                      {t("dataModal.noDataAvailable")}
                    </td>
                  </tr>}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );

}

export default DataModal;
