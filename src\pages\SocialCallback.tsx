import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { showSuccessToast, showErrorToast } from "../components/Toast";
import FacebookPageModal from "../pages/FacebookPageModal";

function SocialCallback() {
  const location = useLocation();
  const navigate = useNavigate();
  const { provider } = useParams();
  const [facebookPages, setFacebookPages] = useState([]);
  const [showPageModal, setShowPageModal] = useState(false);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const code = params.get("code");
    const state = params.get("state");

    if (!provider || !code) {
      showErrorToast("Invalid callback");
      return;
    }

    const userJwtToken = localStorage.getItem('authToken');
    // fetch(`https://app01-sn-nonprod-dev-shared-api-node-twa.azurewebsites.net/linking/${provider}/callback`, {
    const baseURL = import.meta.env.VITE_API_BASE_URL;

    fetch(`${baseURL}/linking/${provider}/callback`, {
      method: "POST",
      headers: { "Content-Type": "application/json", "Authorization": `Bearer ${userJwtToken}` },
      body: JSON.stringify({ code, state }),
    })
      .then((res) => res.json())
      .then((data) => {
        if (provider === 'facebook' && data.pages && data.pages.length > 0) {
          // Show Facebook page selection modal
          setFacebookPages(data.pages);
          setShowPageModal(true);
        } else {
          // For other providers or no pages, proceed normally
          showSuccessToast(`${provider} login successful`);
          navigate("/socialmedia");
        }
      })
      .catch((err) => {
        console.error("Callback error:", err);
        showErrorToast(`${provider} login failed`);
      });
  }, [location, provider, navigate]);

  const handlePageSelection = async (selectedPage: any) => {
    try {
      const userJwtToken = localStorage.getItem('authToken');
      const baseURL = import.meta.env.VITE_API_BASE_URL;

      const response = await fetch(`${baseURL}/linking/facebook/select-page`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${userJwtToken}`
        },
        body: JSON.stringify({
          pageId: selectedPage.id,
          pageToken: selectedPage.accessToken
        }),
      });

      // const response = await fetch(`https://app01-sn-nonprod-dev-shared-api-node-twa.azurewebsites.net/linking/facebook/select-page`, {
      //   method: "POST",
      //   headers: {
      //     "Content-Type": "application/json",
      //     "Authorization": `Bearer ${userJwtToken}`
      //   },
      //   body: JSON.stringify({
      //     pageId: selectedPage.id,
      //     pageToken: selectedPage.accessToken
      //   }),
      // });

      if (response.ok) {
        showSuccessToast("Facebook page selected successfully");
        setShowPageModal(false);
        navigate("/socialmedia");
      } else {
        throw new Error("Failed to select page");
      }
    } catch (error) {
      console.error("Page selection error:", error);
      showErrorToast("Failed to select Facebook page");
    }
  };

  const handleModalClose = () => {
    setShowPageModal(false);
    // Navigate anyway if user closes modal
    navigate("/socialmedia");
  };

  return (
    <>
      <div className="text-center p-10">Processing {provider} login...</div>
      {showPageModal && (
        <FacebookPageModal
          isOpen={showPageModal}
          pages={facebookPages}
          onClose={handleModalClose}
          onSelectPage={handlePageSelection}
        />
      )}
    </>
  );
}

export default SocialCallback;