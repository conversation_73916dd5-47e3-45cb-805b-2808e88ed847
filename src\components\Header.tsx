import { useTranslation } from "react-i18next";
import { Select } from "antd";
import search from "../assets/search.svg";
import logouticon from "../assets/logout.svg";
import { useAuth } from "../context/authContext";
import { useNavigate } from "react-router-dom";

const { Option } = Select;

const Header: React.FC = () => {
  const { t, i18n } = useTranslation();
const { logout } = useAuth();
const navigate = useNavigate();
const handleLogout = () => {
  logout(); 
  navigate("/login"); 
};

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    localStorage.setItem("i18nextLng", lng);
  };

  return (
    <header className="flex items-center justify-between bg-[#F8FAFC] px-4 h-[60px] md:px-6 py-2 w-full">
      <div className="flex-1 max-w-md mx-4 hidden md:flex border-1 border-[#E4E4E4] items-center bg-[#F1F5F9] rounded-[8px] px-4 py-2">
        <img src={search} alt="" />
        <input
          type="text"
          placeholder={t("quickSearch")}
          className="ml-2 w-full bg-transparent focus:outline-none text-sm"
        />
      </div>

      <div className="flex items-center space-x-3">
        <span>{JSON.parse(localStorage.getItem("UserData") || '{}').userName}</span>
        <button className="btn" onClick={handleLogout} >
          <img src={logouticon} alt="" />
        </button>

        <Select
          defaultValue={localStorage.getItem("i18nextLng") || "en"}
          onChange={changeLanguage}
          className="w-32"
        >
          <Option value="en">English</Option>
          <Option value="fr">Français</Option>
          <Option value="de">Deutsch</Option>
        </Select>
      </div>
    </header>
  );
};

export default Header;
