import { useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { useAuth } from "../context/authContext";
import Loader from "./loader";
import { showErrorToast } from "../components/Toast";

const CallbackPage = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  const hasFetched = useRef(false);
  console.log("SocialCallback mounted");

  useEffect(() => {
    if (hasFetched.current) return;
    hasFetched.current = true;

    const queryParams = new URLSearchParams(window.location.search);
    const authCode = queryParams.get("code");
    const core = queryParams.get("core") || "default-core";
    console.log("Auth Code ", authCode);
    if (authCode) {
      // const url = `https://app01-sn-nonprod-dev-shared-api-node-twa.azurewebsites.net/auth/callback?code=${encodeURIComponent(authCode)}&core=${encodeURIComponent(core)}`;
      const baseURL = import.meta.env.VITE_API_BASE_URL;
      const url = `${baseURL}/auth/callback?code=${encodeURIComponent(authCode)}&core=${encodeURIComponent(core)}`;

      axios
        .get(url, {
          headers: {
            Accept: "application/json",
          },
        })
        .then((response) => {
          console.log(response);

          const token = response.data?.token;
          const msAccessToken = response.data?.ms_access_token;

          const data = {
            userName: response.data?.username,
            email: response.data.userEmail,
            role: response.data?.role || "Admin",
            pkg: response.data?.pkg || "default",
            ms_access_token: response.data?.ms_access_token || "",
            client_name: response.data?.client_name || "2WayAnalytics",
          }
          if (token) {
            localStorage.setItem("authToken", token);
            localStorage.setItem("ms-access-token", msAccessToken);
            localStorage.setItem("UserData", JSON.stringify(data));
            login(token);
            localStorage.setItem("i18nextLng", 'en');
            const newUserFlag = localStorage.getItem("new_user");
            if (newUserFlag === "true") {
              navigate("/socialmedia");
              localStorage.removeItem("new_user");
            } else {
              navigate("/analaytics");
            }
          } else {
            console.error("Access token not found in response");
            showErrorToast("Access token not found. Please try logging in again.");
            setTimeout(() => {
              navigate("/login");
            }, 2000);
          }
        })
        .catch((err) => {
          console.error("Error exchanging code for token:", err);
          let message = "An unknown error occurred during login.";

          if (err.response) {
            const status = err.response.status;
            switch (status) {
              case 400:
                message = "Authorization code missing.";
                break;
              case 401:
                message = "Authentication failed or user email not found.";
                break;
              case 404:
                message = "User email not found in the database.";
                break;
              default:
                message = `Login failed with status ${status}.`;
            }
          }
          showErrorToast(message);

          // Redirect to login page after showing error message
          setTimeout(() => {
            navigate("/login");
          }, 2000); // Wait 2 seconds to allow user to read the error message

        });
    } else {
      console.error("Authorization code not found in URL");
      showErrorToast("Authorization code not found. Please try logging in again.");
      setTimeout(() => {
        navigate("/login");
      }, 2000);
    }
  }, [navigate, login]);

  return <Loader fullScreen />;
};

export default CallbackPage;
