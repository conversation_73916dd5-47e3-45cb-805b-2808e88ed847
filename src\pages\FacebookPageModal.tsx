import { CircleX } from "lucide-react";
import { useState } from "react";
import facebookIcon from "../assets/Facebook.svg";

interface FacebookPage {
  id: string;
  name: string;
  accessToken: string;
}

interface FacebookPageModalProps {
  isOpen: boolean;
  pages: FacebookPage[];
  onClose: () => void;
  onSelectPage: (page: FacebookPage) => void;
}

export default function FacebookPageModal({
  isOpen,
  pages,
  onClose,
  onSelectPage,
}: FacebookPageModalProps) {
  const [selectedPageId, setSelectedPageId] = useState<string>("");

  if (!isOpen || !pages || pages.length === 0) return null;

  const handlePageSelect = (pageId: string) => {
    setSelectedPageId(pageId);
  };

  const handleConfirmSelection = () => {
    if (!selectedPageId) {
      return;
    }
    
    const selectedPage = pages.find(page => page.id === selectedPageId);
    if (selectedPage) {
      onSelectPage(selectedPage);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-[#00000052] bg-opacity-50 z-1000">
      <div className="bg-white pt-4 pb-10 px-6 rounded-3xl shadow-lg max-w-[60%] w-full xs:max-sm:max-w-[90%] max-h-[90vh] overflow-hidden">
        <div className="flex justify-between items-center">
          <h2 className="text-[21px] font-semibold tracking-tight noto-sans">
            Select Facebook Page
          </h2>
          <CircleX size={28} onClick={onClose} className="cursor-pointer" />
        </div>

        <div className="mt-4 mb-4">
          <p className="text-gray-600 text-sm">
            Choose which Facebook page you want to manage:
          </p>
        </div>

        <div className="flex items-end gap-3 justify-between mt-2">
          <button
            className={`flex items-center py-3 px-4 justify-center gap-2 btn text-white text-md font-extralight rounded-xl ${
              selectedPageId ? 'bg-primary hover:bg-primary-dark' : 'bg-gray-400 cursor-not-allowed'
            }`}
            onClick={handleConfirmSelection}
            disabled={!selectedPageId}
          >
            Select Page
          </button>
        </div>

        {/* Pages Table */}
        <div className="overflow-x-auto mt-4 max-h-[400px] overflow-y-auto border border-gray-200 rounded-lg scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
          <table className="w-full border-collapse text-left p-4">
            <thead className="sticky top-0 bg-white shadow-md">
              <tr className="border-b border-gray-200 bg-lightblue font-extralight text-md">
                <th className="p-3 font-extralight">Select</th>
                <th className="p-3 font-extralight">Platform</th>
                <th className="p-3 font-extralight">Page Name</th>
                <th className="p-3 font-extralight">Page ID</th>
              </tr>
            </thead>
            <tbody>
              {pages.map((page) => (
                <tr
                  key={page.id}
                  className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                  onClick={() => handlePageSelect(page.id)}
                >
                  <td className="p-3">
                    <input
                      type="radio"
                      name="selectedPage"
                      className="form-control peer border border-gray-200 w-[20px] h-[20px]"
                      checked={selectedPageId === page.id}
                      onChange={() => handlePageSelect(page.id)}
                    />
                  </td>
                  <td className="p-3">
                    <img
                      src={facebookIcon}
                      alt="Facebook"
                      className="h-[40px] w-[40px]"
                    />
                  </td>
                  <td className="p-3">
                    <p className="font-medium">{page.name}</p>
                  </td>
                  <td className="p-3 text-gray-500">
                    {page.id}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}