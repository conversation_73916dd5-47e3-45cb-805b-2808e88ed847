import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Play, Plus, BarChart3, CheckCircle, ArrowRight, <PERSON>rkles } from "lucide-react";

const steps = [
  {
    title: "Welcome to TaskFlow",
    subtitle: "Your Ultimate Productivity Companion",
    description: "Discover how this powerful app transforms the way you manage tasks and boost your productivity. Get ready to experience seamless task management like never before.",
    icon: <Sparkles className="w-12 h-12" />,
  },
  {
    title: "Add Tasks Effortlessly",
    subtitle: "One Click, Endless Possibilities",
    description: "Simply click the + button to add new tasks. Our intuitive interface makes task creation quick and enjoyable. Organize your thoughts and turn them into actionable items.",
    icon: <Plus className="w-12 h-12" />,
  },
  {
    title: "Track Your Progress",
    subtitle: "Real-time Insights & Analytics",
    description: "Monitor your task completion with beautiful visual indicators and real-time progress tracking. Watch your productivity soar as you complete more tasks each day.",
    icon: <BarChart3 className="w-12 h-12" />,
  },
];

const InstructionPage = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [autoPlay, setAutoPlay] = useState(false);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (autoPlay) {
      interval = setInterval(() => {
        setCurrentStep((prev) => (prev + 1) % steps.length);
      }, 4000);
    }
    return () => clearInterval(interval);
  }, [autoPlay]);

  const handleStepChange = (newStep:any) => {
    if (newStep === currentStep) return;
    setIsAnimating(true);
    setTimeout(() => {
      setCurrentStep(newStep);
      setIsAnimating(false);
    }, 150);
  };

  const next = () => {
    const nextStep = currentStep < steps.length - 1 ? currentStep + 1 : 0;
    handleStepChange(nextStep);
  };

  const prev = () => {
    const prevStep = currentStep > 0 ? currentStep - 1 : steps.length - 1;
    handleStepChange(prevStep);
  };

  const currentStepData = steps[currentStep];
  const darkBlue = 'rgb(16, 53, 100)';
  const lightBlue = 'rgb(1, 184, 239)';

  return (
    <div className="min-h-screen bg-white py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{ color: darkBlue }}>
            Getting Started Guide
          </h1>
          <p className="text-xl max-w-2xl mx-auto" style={{ color: darkBlue, opacity: 0.7 }}>
            Follow these simple steps to master your productivity workflow
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-12">
          <div className="flex items-center justify-center space-x-4 mb-6">
            {steps.map((_, index) => (
              <div key={index} className="flex items-center">
                <button
                  onClick={() => handleStepChange(index)}
                  className={`w-12 h-12 rounded-full border-2 flex items-center justify-center font-semibold transition-all duration-300 ${
                    index === currentStep
                      ? "text-white border-transparent shadow-lg scale-110"
                      : index < currentStep
                      ? "text-white border-transparent"
                      : "bg-white border-2 hover:shadow-md"
                  }`}
                  style={{
                    backgroundColor: index === currentStep 
                      ? lightBlue 
                      : index < currentStep 
                      ? darkBlue 
                      : 'white',
                    borderColor: index <= currentStep ? 'transparent' : darkBlue,
                    color: index <= currentStep ? 'white' : darkBlue
                  }}
                >
                  {index < currentStep ? (
                    <CheckCircle className="w-6 h-6" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </button>
                {index < steps.length - 1 && (
                  <div 
                    className="w-16 h-1 mx-2 rounded-full transition-all duration-500"
                    style={{
                      backgroundColor: index < currentStep ? darkBlue : 'rgb(16, 53, 100, 0.2)'
                    }}
                  />
                )}
              </div>
            ))}
          </div>
          
          {/* Progress Percentage */}
          <div className="text-center">
            <span className="text-sm font-medium" style={{ color: darkBlue, opacity: 0.7 }}>
              Step {currentStep + 1} of {steps.length} ({Math.round(((currentStep + 1) / steps.length) * 100)}% Complete)
            </span>
          </div>
        </div>

        {/* Main Content Card */}
        <div className={`bg-white rounded-3xl p-8 md:p-12 shadow-2xl border-2 transition-all duration-500 ${isAnimating ? 'opacity-50 scale-95' : 'opacity-100 scale-100'}`}
             style={{ 
               borderColor: lightBlue,
               boxShadow: `0 25px 50px -12px rgba(1, 184, 239, 0.15)`
             }}>
          <div className="text-center">
            {/* Icon */}
            <div 
              className="inline-flex items-center justify-center w-24 h-24 rounded-full text-white mb-8 shadow-lg"
              style={{ backgroundColor: currentStep % 2 === 0 ? lightBlue : darkBlue }}
            >
              {currentStepData.icon}
            </div>

            {/* Content */}
            <h2 className="text-3xl md:text-4xl font-bold mb-3" style={{ color: darkBlue }}>
              {currentStepData.title}
            </h2>
            <h3 className="text-xl font-semibold mb-6" style={{ color: lightBlue }}>
              {currentStepData.subtitle}
            </h3>
            <p className="text-lg leading-relaxed max-w-2xl mx-auto mb-8" style={{ color: darkBlue, opacity: 0.8 }}>
              {currentStepData.description}
            </p>

            {/* Visual Indicator */}
            <div className="flex justify-center mb-8">
              <div 
                className="w-2 h-2 rounded-full animate-pulse"
                style={{ backgroundColor: lightBlue }}
              ></div>
            </div>
          </div>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-between mt-12">
          <button
            onClick={prev}
            className="flex items-center space-x-2 px-6 py-3 bg-white rounded-xl shadow-lg hover:shadow-xl border-2 transition-all duration-300 hover:translate-y-[-2px]"
            style={{ 
              borderColor: darkBlue,
              color: darkBlue
            }}
            onMouseEnter={(e) => {
              const btn = e.target as HTMLButtonElement;
              btn.style.backgroundColor = darkBlue;
              btn.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              const btn = e.target as HTMLButtonElement;
              btn.style.backgroundColor = 'white';
              btn.style.color = darkBlue;
            }}
          >
            <ChevronLeft className="w-5 h-5" />
            <span className="font-medium">Previous</span>
          </button>

          {/* Auto-play Toggle */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setAutoPlay(!autoPlay)}
              className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 border-2"
              style={{
                backgroundColor: autoPlay ? lightBlue : 'white',
                borderColor: lightBlue,
                color: autoPlay ? 'white' : lightBlue
              }}
            >
              <Play className={`w-4 h-4 ${autoPlay ? 'animate-pulse' : ''}`} />
              <span className="text-sm font-medium">{autoPlay ? 'Auto-playing' : 'Auto-play'}</span>
            </button>
            
            <div className="text-sm" style={{ color: darkBlue, opacity: 0.7 }}>
              {currentStep + 1}/{steps.length}
            </div>
          </div>

          <button
            onClick={next}
            className="flex items-center space-x-2 px-6 py-3 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-2px]"
            style={{ backgroundColor: lightBlue }}
            onMouseEnter={(e) => {
              (e.target as HTMLButtonElement).style.backgroundColor = darkBlue;
            }}
            onMouseLeave={(e) => {
              (e.target as HTMLButtonElement).style.backgroundColor = lightBlue;
            }}
          >
            <span className="font-medium">
              {currentStep === steps.length - 1 ? 'Start Over' : 'Next'}
            </span>
            {currentStep === steps.length - 1 ? (
              <ArrowRight className="w-5 h-5" />
            ) : (
              <ChevronRight className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Step Indicators (Dots) */}
        <div className="flex justify-center space-x-3 mt-8">
          {steps.map((_, index) => (
            <button
              key={index}
              onClick={() => handleStepChange(index)}
              className="w-3 h-3 rounded-full transition-all duration-300"
              style={{
                backgroundColor: index === currentStep ? lightBlue : 'rgba(16, 53, 100, 0.3)',
                transform: index === currentStep ? 'scale(1.25)' : 'scale(1)'
              }}
            />
          ))}
        </div>

        {/* Completion Message */}
        {currentStep === steps.length - 1 && (
          <div className="mt-12 text-center">
            <div 
              className="text-white rounded-2xl p-6 max-w-md mx-auto"
              style={{ backgroundColor: darkBlue }}
            >
              <CheckCircle className="w-8 h-8 mx-auto mb-3" />
              <h3 className="text-lg font-semibold mb-2">Congratulations! 🎉</h3>
              <p style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                You've completed the tutorial. Ready to boost your productivity?
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InstructionPage;