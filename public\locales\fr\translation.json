{"quickSearch": "Recherche rapide...", "logout": "Déconnexion", "selectLanguage": "Choisir la langue", "welcomeMessage": "<PERSON> retour, <PERSON>", "dashboardDescription": "Mesurez le retour sur investissement publicitaire et analysez le trafic de votre site Web.", "dailyPosts": "Publications quotidiennes", "home": "Accueil", "groupPost": "Publications de groupe", "linkEntities": "<PERSON>r les entités", "publications": "Publications", "entities": "Entités", "settings": "Paramètres", "analytics": "Analytique", "alerts": "<PERSON><PERSON><PERSON>", "profile-settings": "Paramètres du profil", "profile-details": "<PERSON><PERSON><PERSON> du profil", "sentiment": "Sentiment", "mentions": "Mentions", "scope": "Portée", "metrics": "Mesures", "characteristics": "Caractéristiques", "main_dashboard": "<PERSON>au de bord principal", "settingsEntities": "Paramètres des entités", "attributes": "Attributs", "allAttributes": "Tous les attributs", "socialMedia": "Réseaux sociaux", "instructions": "Instructions", "contactUs": "<PERSON><PERSON>", "validPosts": "Publications valides", "invalidPosts": "Publications invalides", "text": "Texte", "createdTime": "Heure de création", "status": "Statut", "thumbnail": "Vignette", "actions": "Actions", "published": "<PERSON><PERSON><PERSON>", "unpublished": "Non publié", "edit": "Modifier", "check": "Vérifier", "toggle": "Basculer", "table": {"title": "<PERSON><PERSON><PERSON>", "columns": {"name": "Nom", "email": "E-mail", "role": "R<PERSON><PERSON>", "status": "Statut", "actions": "Actions"}, "status": {"active": "Actif", "inactive": "Inactif"}, "actions": {"edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "welcome": {"title": "<PERSON> retour, {{name}}", "description": "Mesurez votre retour sur investissement publicitaire et analysez le trafic de votre site Web."}}, "groupPosts": {"search": "Texte des publications", "channel": "Plateforme", "filters": {"all": "Toutes les dates", "date_filter": "Plage de dates", "daily_posts": "Publications d'aujourd'hui", "last_week_posts": "7 derniers jours", "last_month_posts": "30 derniers jours", "custom_date": "<PERSON><PERSON>", "begin": "D<PERSON>but", "end": "Fin", "type": "Type de validation", "button": "<PERSON><PERSON><PERSON>"}, "table": {"thumbnail": "Vignette", "columns": {"name": "Texte des publications", "email": "Plateforme", "status": "Statut", "actions": "<PERSON><PERSON><PERSON>"}, "createdTime": "Date de création", "view_original": "Voir la publication originale", "no_data": "<PERSON><PERSON><PERSON> donnée disponible"}, "status": {"all": "Tous", "validated": "<PERSON><PERSON><PERSON>", "unvalidated": "Non validé", "partially_validated": "Partiellement validé"}}, "publication": {"searchPosts": "Rechercher des Texte...", "dateFilter": "Plage de dates", "text": "Texte", "socialMedia": "Plateforme", "status": "Statut", "createdTime": "Date de création", "linkCount": "Nombre de liens", "actions": "<PERSON><PERSON><PERSON>", "thumbnail": "Miniature", "dailyPosts": "Publications d'aujourd'hui", "lastWeekPosts": "7 derniers jours", "lastMonthPosts": "30 derniers jours", "customDate": "<PERSON><PERSON>", "beginDate": "D<PERSON>but", "endDate": "Fin", "postType": "Type de validation", "filterButton": "<PERSON><PERSON><PERSON>", "noPublicationsFound": "Aucune publication trouvée", "entitiesModalTitle": "Attributs liés à la publication", "loading": "Chargement...", "noDataAvailable": "Au<PERSON>ne donnée disponible.", "entityKey": "Clé de l'entité", "entityValue": "Valeur de l'entité", "validationOptions": {"all": "Toutes les dates", "validated": "<PERSON><PERSON><PERSON>", "unvalidated": "Non validé", "partiallyValidated": "Partiellement validé"}}, "entity": {"linkEntitiesToPostTitle": "Entités", "value": "<PERSON><PERSON>", "numberOfLinks": "<PERSON><PERSON> (#)", "actions": "Voir le contenu", "link": "<PERSON><PERSON>", "noDataAvailable": "<PERSON><PERSON><PERSON> donnée disponible", "linkButton": "Lier l'entité", "modalTitle": "Lier l'entité à la publication"}, "dataModal": {"title": "Publications liées à l'entité:", "copy": "<PERSON><PERSON><PERSON>", "platform": "Plateforme", "timestamp": "Horodatage", "noDataAvailable": "<PERSON><PERSON><PERSON> donnée disponible"}, "settingsEntitie": {"settingsEntitiesTitle": "<PERSON><PERSON><PERSON> les entités", "addOnEntity": "Ajouter à l'entité", "addNewKeyLabel": "Ajouter une nouvelle clé", "updateButton": "Modifier", "submitButton": "Valider", "enterNewKeyPlaceholder": "Saisir une nouvelle clé", "entityKeyHeader": "Entité", "imageHeader": "Image", "actionsHeader": "Modifier", "deleteHeader": "Statut", "deleteTooltip": "Activer / Désactiver", "enableButton": "Activer", "disableButton": "Désactiver", "enableTooltip": "Cliquez pour activer cette entité", "disableTooltip": "Cliquez pour désactiver cette entité", "addButton": "Ajouter", "update": "Mettre à jour", "editEntity": "Modifier l'entité", "noData": "<PERSON><PERSON><PERSON> donnée disponible"}, "linkEntitie": {"linkEntitiesToPostTitle": "Lier des entités au post", "linkEntitiesButton": "Lier des entités", "cancelButton": "Annuler", "playerTitle": "<PERSON><PERSON><PERSON>", "playerName": "<PERSON>"}, "socialmedia": {"socialMediaLinkingTitle": "Lien avec les réseaux sociaux", "youtubeLoginButton": "Connecter YouTube", "youtubeLogoutButton": "Déconnecter YouTube", "xLoginButton": "Connecter X (Twitter)", "xLogoutButton": "Déconnecter X (Twitter)", "facebookLoginButton": "Connecter <PERSON>a", "facebookLogoutButton": "Déconnecter Meta", "loggingOut": "Déconnexion en cours...", "permissions": "Autorisations :", "youtubePermissions": "Afficher les informations de base de votre chaîne, téléverser des vidéos et accéder aux analyses.", "xPermissions": "Accéder à votre profil et publier des mises à jour sur votre compte X.", "facebookPermissions": "<PERSON><PERSON><PERSON> les pages, publier des posts et voir les statistiques de page."}, "SettingsAttributes": {"title": "Gestion des attributs", "addAttribute": "Ajouter un attribut", "enterAttribute": "<PERSON><PERSON> un attribut", "selectEntity": "Entité sélectionnée", "allEntities": "Toutes les entités", "noDataAvailable": "<PERSON><PERSON><PERSON> donnée disponible", "attributeSearchPlaceholder": "Rechercher des attributs...", "save": "Enregistrer", "update": "Mettre à jour", "delete": "<PERSON><PERSON><PERSON><PERSON>", "entityKey": "Entité", "entityName": "Attributs", "image": "Image", "actions": "<PERSON><PERSON><PERSON>", "enableTooltip": "Cliquez pour activer cet attribut", "disableTooltip": "Cliquez pour désactiver cet attribut", "errorFetchingData": "Erreur lors de la récupération des données", "errorSavingAttribute": "Erreur lors de l'enregistrement de l'attribut", "errorDeletingAttribute": "E<PERSON>ur lors de la suppression de l'attribut", "updateAttribute": "Mettre à jour l'attribut", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet attribut ?"}, "profilesettings": {"profileSettings": "Paramètres du profil", "timezone": "<PERSON><PERSON> ho<PERSON>", "timezoneTooltip": "Sélectionnez votre fuseau horaire local pour un affichage correct de l'heure dans l'application", "targetLanguage": "<PERSON><PERSON>", "targetLanguageTooltip": "Définissez votre langue préférée pour l'affichage et la traduction du contenu", "minCommentLength": "Longueur minimale du commentaire", "minCommentLengthTooltip": "Définissez le nombre minimum de caractères pour qu'un commentaire soit valide", "maxCommentLength": "Longueur maximale du commentaire", "maxCommentLengthTooltip": "Définissez le nombre maximal de caractères autorisés pour les commentaires (0 signifie aucune limite)", "saveSettings": "Enregistrer les paramètres", "saving": "Enregistrement...", "inviteMembers": "Inviter des membres", "inviteMemberModalLabel": "Inviter un nouveau membre", "username": "Nom d'utilisateur", "usernameTooltip": "Entrez le nom d'utilisateur de la personne à inviter", "email": "Email", "emailTooltip": "Entrez l'adresse e-mail où l'invitation sera envoyée", "role": "R<PERSON><PERSON>", "roleTooltip": "Sélectionnez le niveau de permission pour l'utilisateur invité", "sendInvitation": "Envoyer l'invitation", "alertSettings": "Paramètres d'alerte", "enableAlerts": "<PERSON><PERSON> les alertes", "shiftDetectionAlert": "Alerte de détection de changement", "shiftDetectionTooltip": "Recevoir des notifications lorsque des changements significatifs sont détectés", "impactDetectionAlert": "Alerte de détection d'impact", "impactDetectionTooltip": "Recevoir des notifications lors d'événements à fort impact", "notificationChannel": "Canal de notification", "notificationChannelTooltip": "Sélectionnez comment vous souhaitez recevoir les alertes", "slack": "<PERSON><PERSON>ck", "webhook": "Webhook", "sentimentRange": "Personnaliser la plage de sentiment neutre (en %)", "sentimentRangeTooltip": "Sélectionnez la plage de sentiment neutre (entre négatif et positif).", "min": "Min", "max": "Max", "negativeSentiment": "Sentiment négatif", "neutralSentiment": "Sentiment neutre", "positiveSentiment": "Sentiment positif"}, "alertConfig": {"newAlertConfiguration": "Nouvelle configuration d'alerte", "editAlertConfiguration": "Modifier la configuration de l'alerte", "alertConfiguration": "Configuration d'alerte", "alertScope": "Portée de l'alerte", "allContent": "Tout le contenu", "entity": "Entité", "content": "Contenu", "entityType": "Type d'entité", "entityAttribute": "Attribut d'entité", "contentChannel": "Canal de contenu", "postDate": "Date de publication", "allDates": "Toutes les dates", "postText": "Texte de la publication", "metrics": "Métriques", "metric": "Métrique", "comments": "Commentaires", "likes": "Mentions J’aime", "shares": "Partages", "views": "<PERSON><PERSON>", "engagements": "Engagements", "threshold": "<PERSON><PERSON>", "alertCharacteristics": "Caractéristiques de l'alerte", "duration": "<PERSON><PERSON><PERSON>", "noDuration": "<PERSON><PERSON><PERSON>", "1h": "1 heure", "2h": "2 heures", "4h": "4 heures", "12h": "12 heures", "1d": "1 jour", "2d": "2 jours", "7d": "7 jours", "30d": "30 jours", "repeatable": "Répétable", "startDate": "Date de début", "endDate": "Date de fin", "selectDate": "Sélectionner une date", "saveAlert": "Enregistrer l'alerte", "allChannels": "To<PERSON> les canaux", "saveEntity": "Enregistrer l'entité", "saveMetric": "Enregistrer la métrique", "actions": "Actions", "remove": "<PERSON><PERSON><PERSON><PERSON>", "XTwitter": "X (Twitter)", "facebook": "Facebook", "instagram": "Instagram", "youtube": "YouTube", "player": "<PERSON><PERSON><PERSON>", "campaign": "Campagne", "product": "Produit", "wordCount": "Nombre de mots", "commentCount": "Nombre de commentaires", "trackedMentions": "Mentions suivies", "enterTrackedWords": "Entrez des mots suivis (par ex. noms de produits)", "trackedWords": "<PERSON><PERSON> suivis (optionnel)", "sentimentScore": "Score de sentiment (%)", "alertType": "Type d’alerte", "above": "Au-dessus", "below": "En dessous", "optionalValue": "<PERSON><PERSON>e (doit être >10)"}, "alerts-tablet": {"date_filter": "Filtre de date", "daily_posts": "<PERSON><PERSON><PERSON> quotidiennes", "last_week_posts": "Alertes de la dernière semaine", "last_month_posts": "<PERSON><PERSON><PERSON> du dernier mois", "custom_date": "Date personnalisée", "begin": "Date de début", "end": "Date de fin", "scope": "Portée", "alert_count": "Nombre d'alertes", "type": "Type", "button": "<PERSON><PERSON><PERSON>", "Search Posts": "Rechercher des alertes", "thumbnail": "Vignette", "content": "Contenu", "channel": "Canal", "endDate": "Date de fin", "metricType": "Type de métrique", "metrics": "Métriques", "status": "Statut", "actions": "Actions", "Add Sentiments": "Ajouter des sentiments", "Add Metrics": "Ajouter des métriques", "Add Mention": "Ajouter une mention", "enableTooltip": "Cliquez pour activer cette alerte", "disableTooltip": "Cliquez pour désactiver cette alerte"}, "userManagement": {"title": "Gestion des utilisateurs", "inviteMembers": "Inviter des membres", "inviteDisabledTooltip": "Cette fonctionnalité n'est pas disponible dans votre formule actuelle", "inviteMemberModalLabel": "Inviter un nouveau membre", "username": "Nom d'utilisateur", "email": "Email", "role": "R<PERSON><PERSON>", "roleTooltip": "Attribuez un rôle au nouveau membre. L'Administrateur a un contrôle total, l'Utilisateur a un accès limité.", "sendInvitation": "Envoyer l'invitation", "usernameHeader": "Nom d'utilisateur", "emailHeader": "Email", "roleHeader": "R<PERSON><PERSON>", "actionHeader": "Action", "noUsers": "Aucun utilisateur trouvé.", "emptyFieldsError": "Le nom d'utilisateur et l'email ne peuvent pas être vides.", "invalidEmailError": "Veu<PERSON>z entrer une adresse email valide.", "inviteSuccess": "Utilisateur invité avec succès !", "inviteError": "Erreur lors de l'invitation de l'utilisateur. Veuillez réessayer.", "invalidRoleError": "Rôle invalide. Les rôles autorisés sont 'Administrateur' et 'Utilisateur'.", "forbiddenInviteError": "Seuls les administrateurs peuvent envoyer des invitations.", "inviterNotFoundError": "L'utilisateur qui invite est introuvable dans le système.", "userExistsError": "Un utilisateur avec cet email ou ce nom d'utilisateur existe déjà.", "serverError": "Échec de l'invitation de l'utilisateur. Veuillez réessayer plus tard.", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet utilisateur ?"}}