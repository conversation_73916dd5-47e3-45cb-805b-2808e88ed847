// import { ReactNode, useState } from "react";
// import { useTranslation } from "react-i18next";
// import { RxHamburgerMenu } from "react-icons/rx";
// import { useNavigate } from "react-router-dom";
// import logoutIcon from "../assets/images/logout.png";
// import logo from "../assets/logo.svg";
// import Dropdown from "../components/Dropdown";
// import Sidebar from "../components/Sidebar";
// import { useAuth } from "../context/authContext";

// const MainLayout = ({ children }: { children: ReactNode }) => {
//   const [isSidebarVisible, setIsSidebarVisible] = useState(false);
//   const { i18n } = useTranslation();
//   const { logout } = useAuth();
//   const navigate = useNavigate();
//   const handleLogout = () => {
//     logout();
//     navigate("/login");
//   };
//   const toggleSidebar = () => {
//     setIsSidebarVisible(!isSidebarVisible);
//   };
//   return (
//     <div className="flex h-screen p-2 xs:max-md:p-0 bg-[#F4F7FB]">
//       <Sidebar showSidebar={isSidebarVisible} onClose={toggleSidebar} />
//       <div className="flex-1 px-5 xs:max-md:min-h-screen sm:max-md:px-6 pt-1 overflow-y-auto xs:max-md:px-2">
//         {/* Header */}
//         <img
//           src={logo}
//           alt="Logo"
//           className="object-contain max-w-[200px] hidden xs:max-md:flex mx-auto my-4"
//         />
//         <div className="flex sm:max-md:items-center py-6 hidden xs:max-md:flex px-4 relative bg-white w-full justify-between rounded-2xl">
//           <div className="w-[100px] sm:max-md:w-[150px]">
//             <Dropdown
//               value={i18n.language}
//               className="max-h-[45px] !bg-[#F4F7FB] border-none rounded-xl"
//               options={[
//                 { label: "English", value: "en" },
//                 { label: "German", value: "de" },
//                 { label: "Français", value: "fr" },
//               ]}
//               iconStyles="text-black"
//               onChange={(lang: string | number) =>
//                 i18n.changeLanguage(String(lang))
//               }
//             />
//           </div>
//           <div className="flex-1 flex items-center justify-between">
//             <span className="flex items-center gap-1.5 sm:max-md:gap-3">
//               <div className="w-[1px] h-[35px] bg-gray-200 ml-2"> </div>
//               {/* <img
//                 src="https://wac-cdn.atlassian.com/dam/jcr:************************b2015223c918/Max-R_Headshot%20(1).jpg?cdnVersion=2664"
//                 alt="profile"
//                 className="rounded-full h-10 w-10 sm:max-md:h-14 sm:max-md:w-14"
//               /> */}
//               <span className="text-sm sm:max-md:text-lg">{JSON.parse(localStorage.getItem("UserData") || '{}').userName || "Ihtizaz"}</span>
//               <img src={logoutIcon} alt="logout-icon" onClick={handleLogout} />
//             </span>
//             <RxHamburgerMenu
//               className="text-2xl flex text-gray-700"
//               onClick={toggleSidebar}
//             />
//           </div>
//         </div>
//         <main className="sm:max-md:mt-4">{children}</main>
//       </div>
//     </div>
//   );
// };
// export default MainLayout;


import { ReactNode, useState } from "react";
import { useTranslation } from "react-i18next";
import { RxHamburgerMenu } from "react-icons/rx";
import { useNavigate } from "react-router-dom";
import logoutIcon from "../assets/images/logout.png";
import logo from "../assets/logo.svg";
import Dropdown from "../components/Dropdown";
import Sidebar from "../components/Sidebar";
import { useAuth } from "../context/authContext";

const MainLayout = ({ children }: { children: ReactNode }) => {
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const { i18n } = useTranslation();
  const { logout } = useAuth();
  const navigate = useNavigate();
  const handleLogout = () => {
    logout();
    navigate("/login");
  };
  const toggleSidebar = () => {
    setIsSidebarVisible(!isSidebarVisible);
  };
  return (
    <div className="flex h-screen p-2 xs:max-md:p-0 bg-[#F4F7FB]">
      <Sidebar showSidebar={isSidebarVisible} onClose={toggleSidebar} />
      <div className="flex-1 px-3 xs:max-md:min-h-screen sm:max-md:px-6 pt-1 overflow-y-auto xs:max-md:px-2">
        {/* Header */}
        <img
          src={logo}
          alt="Logo"
          className="object-contain max-w-[200px] hidden xs:max-md:flex mx-auto my-4"
        />
        <div className="flex sm:max-md:items-center py-6 hidden xs:max-md:flex px-4 relative bg-white w-full justify-between rounded-2xl">
          <div className="w-[100px] sm:max-md:w-[150px]">
            <Dropdown
              value={i18n.language}
              className="max-h-[45px] !bg-[#F4F7FB] border-none rounded-xl"
              options={[
                { label: "English", value: "en" },
                { label: "German", value: "de" },
                { label: "Français", value: "fr" },
              ]}
              iconStyles="text-black"
              onChange={(lang: string | number) =>
                i18n.changeLanguage(String(lang))
              }
            />
          </div>
          <div className="flex-1 flex items-center justify-between">
            <span className="flex items-center gap-1.5 sm:max-md:gap-3">
              <div className="w-[1px] h-[35px] bg-gray-200 ml-2"> </div>
              {/* <img
                src="https://wac-cdn.atlassian.com/dam/jcr:************************b2015223c918/Max-R_Headshot%20(1).jpg?cdnVersion=2664"
                alt="profile"
                className="rounded-full h-10 w-10 sm:max-md:h-14 sm:max-md:w-14"
              /> */}
              <span className="text-sm sm:max-md:text-lg">{JSON.parse(localStorage.getItem("UserData") || '{}').userName || "Ihtizaz"}</span>
              <img src={logoutIcon} alt="logout-icon" onClick={handleLogout} />
            </span>
            <RxHamburgerMenu
              className="text-2xl flex text-gray-700"
              onClick={toggleSidebar}
            />
          </div>
        </div>
        <main className="sm:max-md:mt-4">{children}</main>
      </div>
    </div>
  );
};
export default MainLayout;
