{"quickSearch": "Quick search...", "logout": "Logout", "selectLanguage": "Select Language", "home": "Home", "groupPost": "Group Posts", "linkEntities": "Link Entities", "publications": "Publications", "entities": "Entities", "settings": "Settings", "alerts": "<PERSON><PERSON><PERSON>", "sentiment": "Sentiment", "profile-settings": "Profile Settings", "profile-details": "Profile Details", "mentions": "Mentions", "scope": "<PERSON><PERSON>", "metrics": "Metrics", "characteristics": "Characteristics", "settingsEntities": "Settings Entities", "attributes": "Attributes", "allAttributes": "All Attributes", "socialMedia": "Social Media", "instructions": "Instructions", "contactUs": "Contact Us", "welcomeMessage": "Welcome back, <PERSON>", "dashboardDescription": "Measure your advertising ROI and report website traffic.", "dailyPosts": "Daily Posts", "validPosts": "Valid <PERSON>s", "invalidPosts": "Invalid Posts", "text": "Text", "createdTime": "Created Time", "status": "Status", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Actions", "published": "Published", "unpublished": "Unpublished", "analytics": "Analytics", "main_dashboard": "Main Dashboard", "edit": "Edit", "check": "Check", "toggle": "Toggle", "table": {"title": "User Data", "columns": {"name": "Text", "email": "Social Network", "role": "Role", "status": "Status", "actions": "Actions"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"edit": "Edit", "delete": "Delete"}}, "welcome": {"title": "Welcome back, {{name}}", "description": "Measure your advertising ROI and report website traffic."}, "groupPosts": {"search": "Search Text", "channel": "Platform", "filters": {"all": "All Dates", "date_filter": "Date Range", "daily_posts": "Today's Posts", "last_week_posts": "Last 7 Days", "last_month_posts": "Last 30 Days", "custom_date": "Custom Range", "begin": "<PERSON><PERSON>", "end": "End", "type": "Validation Type", "button": "Search"}, "table": {"thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "columns": {"name": "Posts Text", "email": "Platform", "status": "Status", "actions": "Manage"}, "createdTime": "Date Created", "view_original": "View Original Post", "no_data": "No data available"}, "status": {"all": "All", "validated": "Validated", "unvalidated": "Unvalidated", "partially_validated": "Partially Validated"}}, "publication": {"searchPosts": "Search Text...", "dateFilter": "Date Range", "text": "Text", "socialMedia": "Platform", "status": "Status", "createdTime": "Date Created", "linkCount": "<PERSON>", "actions": "Manage", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "dailyPosts": "Today's Posts", "lastWeekPosts": "Last 7 Days", "lastMonthPosts": "Last 30 Days", "customDate": "Custom Range", "beginDate": "<PERSON><PERSON>", "endDate": "End", "postType": "Validation Type", "filterButton": "Search", "noPublicationsFound": "No publications found", "entitiesModalTitle": "Linked attributes to publication", "loading": "Loading...", "noDataAvailable": "No data available.", "entityKey": "Entity Key", "entityValue": "Entity Value", "validationOptions": {"all": "All Dates", "validated": "Validated", "unvalidated": "Unvalidated", "partiallyValidated": "Partially Validated"}}, "entity": {"linkEntitiesToPostTitle": "Entities", "value": "Value", "numberOfLinks": "<PERSON> (#)", "actions": "View Content", "link": "Link", "noDataAvailable": "No data available", "linkButton": "Link Entity", "modalTitle": "Link Entity to Publication"}, "dataModal": {"title": "Posts related to <PERSON><PERSON><PERSON>:", "copy": "Copy", "platform": "Platform", "timestamp": "Timestamp", "noDataAvailable": "No data available"}, "settingsEntitie": {"settingsEntitiesTitle": "Manage Entities", "addOnEntity": "Add on Entity", "addNewKeyLabel": "Add New Key", "updateButton": "Edit", "submitButton": "Submit", "enterNewKeyPlaceholder": "Enter New Key", "entityKeyHeader": "Entity", "imageHeader": "Image", "actionsHeader": "Edit", "deleteHeader": "Status", "deleteTooltip": "Enable / Disable", "enableButton": "Enable", "disableButton": "Disable", "enableTooltip": "Click to enable this entity", "disableTooltip": "Click to disable this entity", "addButton": "Add", "update": "Update", "editEntity": "Edit En<PERSON>", "noData": "No data available"}, "linkEntitie": {"linkEntitiesToPostTitle": "Entities", "linkEntitiesButton": "Link Entities", "cancelButton": "Cancel", "playerTitle": "Player", "playerName": "<PERSON>", "numberOfLinks": "# Links"}, "socialmedia": {"socialMediaLinkingTitle": "Social Media Linking", "youtubeLoginButton": "Connect YouTube", "youtubeLogoutButton": "Disconnect YouTube", "xLoginButton": "Connect X (Twitter)", "xLogoutButton": "Disconnect X (Twitter)", "facebookLoginButton": "Connect Meta", "facebookLogoutButton": "Disconnect Meta", "loggingOut": "Logging out...", "permissions": "Permissions:", "youtubePermissions": "View your channel's basic info, upload video data, and access analytics.", "xPermissions": "Access profile and post updates to your X account.", "facebookPermissions": "Manage pages, publish posts, and view page insights."}, "SettingsAttributes": {"title": "Attributes Management", "addAttribute": "Add Attribute", "enterAttribute": "Enter Attribute", "selectEntity": "Selected Entity", "allEntities": "All Entities", "noDataAvailable": "No data available", "attributeSearchPlaceholder": "Search attributes...", "save": "Save", "update": "Update", "delete": "Delete", "entityKey": "Entity", "entityName": "Attributes", "image": "Image", "actions": "Manage", "enableTooltip": "Click to enable this attribute", "disableTooltip": "Click to disable this attribute", "errorFetchingData": "Error fetching data", "errorSavingAttribute": "Error saving attribute", "errorDeletingAttribute": "Error deleting attribute", "updateAttribute": "Update Attribute", "confirmDelete": "Are you sure you want to delete this attribute?"}, "profilesettings": {"profileSettings": "Profile Settings", "timezone": "Timezone", "timezoneTooltip": "Select your local timezone for correct time display in the app", "targetLanguage": "Language", "targetLanguageTooltip": "Set your preferred language for content display and translation", "minCommentLength": "Min Comment Length", "minCommentLengthTooltip": "Set the minimum number of characters required for a valid comment", "maxCommentLength": "Max Comment Length", "maxCommentLengthTooltip": "Set the maximum number of characters allowed in comments (0 means no limit)", "saveSettings": "Save Settings", "saving": "Saving...", "inviteMembers": "Invite Members", "inviteMemberModalLabel": "Invite a New Member", "username": "Username", "usernameTooltip": "Enter the username of the person to invite", "email": "Email", "emailTooltip": "Enter the email address where the invitation will be sent", "role": "Role", "roleTooltip": "Select the permission level for the invited user", "sendInvitation": "Send Invitation", "alertSettings": "<PERSON><PERSON>", "enableAlerts": "Enable <PERSON>", "shiftDetectionAlert": "Shift Detection Alert", "shiftDetectionTooltip": "Receive notifications when significant metric shifts are detected", "impactDetectionAlert": "Impact Detection Alert", "impactDetectionTooltip": "Receive notifications when high-impact events occur", "notificationChannel": "Notification Channel", "notificationChannelTooltip": "Select how you want to receive alerts", "slack": "<PERSON><PERSON>ck", "webhook": "Webhook", "sentimentRange": "Customize neutral sentiment range (in %)", "sentimentRangeTooltip": "Select the neutral sentiment range (between negative and positive).", "min": "Min", "max": "Max", "negativeSentiment": "Negative sentiment", "neutralSentiment": "Neutral sentiment", "positiveSentiment": "Positive sentiment"}, "alertConfig": {"newAlertConfiguration": "New Alert Configuration", "editAlertConfiguration": "Edit <PERSON><PERSON>figu<PERSON>", "alertConfiguration": "Alert Configuration", "alertScope": "<PERSON><PERSON>", "allContent": "All Content", "entity": "Entity", "content": "Content", "entityType": "Entity Type", "entityAttribute": "Entity Attribute", "contentChannel": "Content Channel", "postDate": "Post Date", "allDates": "All Dates", "postText": "Post Text", "metrics": "Metrics", "metric": "Metric", "comments": "Comments", "likes": "<PERSON>s", "shares": "Shares", "views": "Views", "engagements": "Engagements", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "alertCharacteristics": "Alert Characteristics", "duration": "Duration", "noDuration": "No Duration", "1h": "1 hour", "2h": "2 hours", "4h": "4 hours", "12h": "12 hours", "1d": "1 day", "2d": "2 days", "7d": "7 days", "30d": "30 days", "repeatable": "Repeatable", "startDate": "Start Date", "endDate": "End Date", "selectDate": "Select Date", "saveAlert": "Save <PERSON><PERSON>", "allChannels": "All Channels", "saveEntity": "Save Entity", "saveMetric": "Save Metric", "actions": "Actions", "remove": "Remove", "XTwitter": "X (Twitter)", "facebook": "Facebook", "instagram": "Instagram", "youtube": "YouTube", "player": "Player", "campaign": "Campaign", "product": "Product", "wordCount": "Word Count", "commentCount": "Comment Count", "trackedMentions": "Tracked Mentions", "enterTrackedWords": "Enter tracked words (e.g., product names)", "trackedWordsOptional": "Specific Words to Track (Optional)", "enterWords": "Type and press enter to add words", "trackedWords": "Tracked Words (Optional)", "sentimentScore": "Sentiment Score (%)", "alertType": "Alert <PERSON>", "above": "Above", "below": "Below", "optionalValue": "Number Of Comments (Optional)"}, "alerts-tablet": {"date_filter": "Date Filter", "daily_posts": "Daily Alerts", "last_week_posts": "Last Week Alerts", "last_month_posts": "Last Month Alerts", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "custom_date": "Custom Date", "begin": "Start Date", "scope": "<PERSON><PERSON>", "alert_count": "<PERSON><PERSON>", "end": "End Date", "type": "Type", "button": "Filter", "Search Posts": "Search Alerts", "content": "Content", "channel": "Channel", "endDate": "End Date", "metricType": "Metric Type", "metrics": "Metrics", "status": "Status", "actions": "Actions", "Add Sentiments": "Add Sentiments", "Add Metrice": "Add Metrics", "Add Mention": "Add Mention", "enableTooltip": "Click to enable this alert", "disableTooltip": "Click to disable this alert"}, "userManagement": {"title": "User Management", "inviteMembers": "Invite Members", "inviteDisabledTooltip": "This feature is not available in your current plan", "inviteMemberModalLabel": "Invite New Member", "username": "Username", "email": "Email", "role": "Role", "roleTooltip": "Assign a role to the new member. Admin has full control, User has limited access.", "sendInvitation": "Send Invitation", "usernameHeader": "Username", "emailHeader": "Email", "roleHeader": "Role", "actionHeader": "Action", "noUsers": "No users found.", "emptyFieldsError": "Username and Email cannot be empty.", "invalidEmailError": "Please enter a valid email address.", "inviteSuccess": "User invited successfully!", "inviteError": "Error inviting user. Please try again.", "invalidRoleError": "Invalid role. Allowed roles are 'Admin' and 'User'.", "forbiddenInviteError": "Only Admin users can send invitations.", "inviterNotFoundError": "Inviter user not found in the system.", "userExistsError": "A user with this email or username already exists.", "serverError": "Failed to invite user. Please try again later.", "confirmDelete": "Are you sure you want to delete this user?"}}