<svg width="85" height="82" viewBox="0 0 85 82" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_di_8005_1783)">
<g clip-path="url(#clip0_8005_1783)">
<rect x="12" y="13" width="48" height="48" rx="12" fill="url(#paint0_linear_8005_1783)" fill-opacity="0.1" shape-rendering="crispEdges"/>
<g style="mix-blend-mode:plus-lighter" opacity="0.5" filter="url(#filter1_f_8005_1783)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.4758 25.5H23.1927L32.8022 38.1096L23 48.5H26.3291L34.2866 40.0609L40.717 48.5H49L38.8694 35.2044L48.0211 25.5H44.6922L37.3811 33.2531L31.4758 25.5ZM41.9383 46.0952L28.0831 27.9088H30.2545L44.1097 46.0952H41.9383Z" fill="black"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.4758 25.5H23.1927L32.8022 38.1096L23 48.5H26.3291L34.2866 40.0609L40.717 48.5H49L38.8694 35.2044L48.0211 25.5H44.6922L37.3811 33.2531L31.4758 25.5ZM41.9383 46.0952L28.0831 27.9088H30.2545L44.1097 46.0952H41.9383Z" fill="black"/>
<g filter="url(#filter2_f_8005_1783)">
<ellipse cx="36.5" cy="60.5" rx="11.5" ry="6.5" fill="black"/>
</g>
</g>
<rect x="12" y="13" width="48" height="48" rx="12" stroke="url(#paint1_linear_8005_1783)" stroke-opacity="0.5" stroke-width="0.8" shape-rendering="crispEdges"/>
</g>
<g style="mix-blend-mode:screen" filter="url(#filter3_f_8005_1783)">
<circle cx="29" cy="30" r="9" fill="black"/>
</g>
<defs>
<filter id="filter0_di_8005_1783" x="-8.40039" y="-7.40039" width="92.8008" height="88.8008" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="4"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8005_1783"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8005_1783" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0509804 0 0 0 0 0.932 0 0 0 0 0.988235 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_8005_1783"/>
</filter>
<filter id="filter1_f_8005_1783" x="8" y="9" width="56" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6" result="effect1_foregroundBlur_8005_1783"/>
</filter>
<filter id="filter2_f_8005_1783" x="7" y="36" width="59" height="49" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="9" result="effect1_foregroundBlur_8005_1783"/>
</filter>
<filter id="filter3_f_8005_1783" x="0" y="1" width="58" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_8005_1783"/>
</filter>
<linearGradient id="paint0_linear_8005_1783" x1="12" y1="13" x2="60" y2="61" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1"/>
</linearGradient>
<linearGradient id="paint1_linear_8005_1783" x1="13" y1="12" x2="58.5" y2="61" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1"/>
</linearGradient>
<clipPath id="clip0_8005_1783">
<rect x="12" y="13" width="48" height="48" rx="12" fill="white"/>
</clipPath>
</defs>
</svg>
