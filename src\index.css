@import "tailwindcss";
.btn {
  cursor: pointer;
}
@theme {
  --color-primary: #01b8ef;
  --color-secondary: #103564;
  --color-gray: #8092ab;
  --color-black: #2a3547;
  --color-lightblue: #e6f6fd;
  --breakpoint-*: initial;
  --breakpoint-xs: 280px;
  --breakpoint-sm: 480px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 991px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl:1350px;

}
@font-face {
  font-family: "Satonshi";
  src: url("./assets/fonts/Satoshi-Variable.ttf") format("truetype");
  font-style: normal;
}
body {
  font-family: "Satonshi", sans-serif;
}
@media screen and (max-width: 900px) {
  table {
    max-width: 880px;
    width: 100%;
    overflow-x: scroll;
  }
}
.noto-sans {
  font-family: "Noto Sans KR", sans-serif;
  font-style: normal;
  font-weight: 600;
}

.youtube-btn {
  width: 217px;
  height: 48px;
  gap: 10px;
  border-radius: 12px;
  border-width: 0.8px;
  padding: 8px;
  color: linear-gradient(
    135deg,
    rgba(248, 251, 255, 0.04) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border: 0.8px solid;
  border-image-source: linear-gradient(
    137.12deg,
    rgba(216, 216, 216, 0.025) -0.08%,
    rgba(255, 255, 255, 0.2) 98.5%
  );
  backdrop-filter: blur(20px);
  box-shadow: 8px 4px 16px 0px #00000014;
  color: #fc111f;
  box-shadow: 0px 0px 8px 0px #fc0d1b52 inset;
  font-weight: 600;
  font-size: 18.6px;
  line-height: 21.71px;
  letter-spacing: 0%;
  text-align: center;
}
.twitter-btn {
  width: 217px;
  height: 48px;
  gap: 10px;
  border-radius: 12px;
  border-width: 0.8px;
  padding: 8px;
  color: linear-gradient(
    135deg,
    rgba(248, 251, 255, 0.04) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border: 0.8px solid;
  border-image-source: linear-gradient(
    137.12deg,
    rgba(216, 216, 216, 0.025) -0.08%,
    rgba(255, 255, 255, 0.2) 98.5%
  );
  backdrop-filter: blur(20px);
  box-shadow: 8px 4px 16px 0px #00000014;
  box-shadow: 0px 0px 8px 0px #0deefc52 inset;
  color: #5a7f9d;
  font-weight: 600;
  font-size: 18.6px;
  line-height: 21.71px;
  letter-spacing: 0%;
  text-align: center;
}
.facebook-btn {
  width: auto;
  height: 48px;
  gap: 10px;
  border-radius: 12px;
  border-width: 0.8px;
  padding: 8px;
  color: linear-gradient(
    135deg,
    rgba(248, 251, 255, 0.04) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border: 0.8px solid;
  border-image-source: linear-gradient(
    137.12deg,
    rgba(216, 216, 216, 0.025) -0.08%,
    rgba(255, 255, 255, 0.2) 98.5%
  );
  backdrop-filter: blur(20px);
  box-shadow: 8px 4px 16px 0px #00000014;
  box-shadow: 0px 0px 8px 0px #287eff52 inset;
  font-weight: 600;
  font-size: 18.6px;
  line-height: 21.71px;
  letter-spacing: 0%;
  text-align: center;
}
.ps-sidebar-root {
  border: none !important;
  height: 100% !important;
}

.ps-submenu-content {
  background-color: #1a3561 !important;
}

.ps-submenu-content .ps-menu-button {
  padding-left: 2rem !important;
}

.ps-menu-button:hover {
  background-color: #8e8e93 !important;
}

.ps-active > .ps-menu-button {
  background-color: #8e8e93 !important;
}

.ps-submenu-expand-icon {
  color: white !important;
}

/* Submenu icon customization */
.ps-submenu-expand-icon span {
  width: 8px !important;
  height: 8px !important;
}

/* Override submenu expand icon with custom icons */
.ps-submenu-expand-icon span::before {
  content: none !important;
}

.ps-submenu-expand-icon span::after {
  content: none !important;
}
.css-dip3t8 {
  background-color: #1a3561 !important;
}
.css-wx7wi4 {
  width: auto !important;
  height: auto !important;
  min-width: auto !important;
  min-height: auto !important;
}
.pagination-container {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.pagination-buttons button,
.pagination-page-size select,
.pagination-jump input {
  padding: 5px 10px;
  margin: 0 3px;
  cursor: pointer;
}

.pagination-buttons .active {
  background-color: #007bff;
  color: white;
}
/* For WebKit-based browsers (Chrome, Safari, Edge) */
.scrollbar-hidden {
  overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
.shadow {
  box-shadow: 0px 6px 16px 2px #0000000d;
}

@layer components {
  .left-col {
    @apply pl-5 pr-4 py-4;
  }

  .right-col {
    @apply pr-5 pl-4 py-4;
  }
}

.report-style-class {
  width: 100%;
  height: 90vh;
  border-radius: 1.5rem; /* Matches rounded-3xl (24px) */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Matches shadow */
}

/* Ensure iframe inherits styles correctly */
.report-style-class iframe {
  width: 100% !important;
  height: 100% !important;
  border: none;
  border-radius: 1.5rem;
}