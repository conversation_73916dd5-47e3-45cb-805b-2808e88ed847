import React, { useEffect } from "react";
import { GoArrowLeft } from "react-icons/go";
import logo from "../assets/logo.svg";
import { useLocation } from "react-router-dom";
import loginImage from '../assets/Login.png';
import apiService from "../services/apiService";

const Login: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const newUserParam = params.get("new_user");

    if (newUserParam === "true") {
      localStorage.setItem("new_user", "true");
    }
  }, [location.search]);

  const handleLogin = () => {
    apiService.login();
  };
  const onNavigateBack = () => { };
  return (
    <div className="w-screen h-screen flex xs:max-sm:min-h-screen">
      <div className="w-1/2 bg-white flex-1 p-8">
        <div className="h-[20%]">
          <button
            className="bg-[#ECF3FB] items-center gap-2 text-md rounded-xl cursor-pointer py-3 px-6 flex"
            onClick={onNavigateBack}
          >
            <GoArrowLeft /> Back to website
          </button>
        </div>
        <div className="justify-center text-[#103564] flex flex-col h-[60%] items-center">
          <img
            src={logo}
            alt="logo"
            className="object-contain w-[350px] xs:max-sm:w-[250px]"
          />
          <h1 className="font-bold text-2xl mt-4">Welcome back,</h1>
          <p className="font-medium text-lg tracking-wide">
            good to see you again!
          </p>
          <button
            className="py-3.5 mt-10 bg-primary cursor-pointer text-white px-20 font-semibold rounded-xl"
            onClick={handleLogin}
          >
            Login
          </button>
        </div>
      </div>
      <div
        className="w-1/2 xs:max-sm:hidden flex-1 bg-cover bg-center"
        style={{ backgroundImage: `url(${loginImage})` }}
      ></div>


      {/* <div className="w-1/2 xs:max-sm:hidden flex-1 bg-[url('https://img.freepik.com/premium-vector/realistic-social-media-elements-collection_23-2151512132.jpg')] bg-cover bg-center"></div> */}
    </div>
  );
};
export default Login;
